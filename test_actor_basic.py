#!/usr/bin/env python3
"""
Basic test for the Actor system to verify it works correctly.

This is a simple test to check if the core Actor functionality
is working before running the full application.
"""

import asyncio
import logging
import sys
import os

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.core.actor import Actor, ActorState
from app.core.event_bus import event_bus
from app.core.message_context import MessageContext, MessageState, message_context_manager

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


class TestActor(Actor):
    """Simple test actor for verification."""
    
    def __init__(self, name):
        super().__init__(name)
        self.messages_received = []
    
    async def initialize(self):
        logger.info(f"{self.name} initializing...")
    
    async def start_actor(self):
        logger.info(f"{self.name} starting...")
    
    async def stop_actor(self):
        logger.info(f"{self.name} stopping...")
    
    @event_bus.subscribe("test_message")
    async def handle_test_message(self, event_data):
        logger.info(f"{self.name} received test message: {event_data}")
        self.messages_received.append(event_data)


async def test_basic_actor():
    """Test basic actor functionality."""
    logger.info("=== Testing Basic Actor Functionality ===")
    
    # Create test actor
    actor = TestActor("TestActor1")
    
    # Test initial state
    assert actor.state == ActorState.CREATED
    assert not actor.is_running()
    
    # Start actor
    await actor.start()
    assert actor.state == ActorState.RUNNING
    assert actor.is_running()
    
    # Test event handling
    await event_bus.publish("test_message", {"content": "Hello from test!"})
    await asyncio.sleep(0.1)  # Give time for event processing
    
    assert len(actor.messages_received) == 1
    assert actor.messages_received[0]["content"] == "Hello from test!"
    
    # Stop actor
    await actor.stop()
    assert actor.state == ActorState.STOPPED
    assert not actor.is_running()
    
    logger.info("✓ Basic Actor functionality test passed!")


async def test_message_context():
    """Test message context tracking."""
    logger.info("=== Testing Message Context Tracking ===")
    
    # Create a message context
    context = message_context_manager.create_context(
        telegram_message_id=12345,
        chat_id=67890,
        message_text="Test message",
        telegram_user_id=111
    )
    
    # Test initial state
    assert context.state == MessageState.RECEIVED
    assert context.telegram_message_id == 12345
    assert context.chat_id == 67890
    assert context.message_text == "Test message"
    
    # Test state updates
    context.update_state(MessageState.USER_IDENTIFYING, "TestActor")
    assert context.state == MessageState.USER_IDENTIFYING
    assert len(context.processing_steps) == 1
    
    # Test context retrieval
    retrieved_context = message_context_manager.get_context(context.context_id)
    assert retrieved_context is not None
    assert retrieved_context.context_id == context.context_id
    
    # Test retrieval by telegram ID
    retrieved_by_telegram = message_context_manager.get_context_by_telegram_id(12345, 67890)
    assert retrieved_by_telegram is not None
    assert retrieved_by_telegram.context_id == context.context_id
    
    logger.info("✓ Message Context tracking test passed!")


async def test_event_bus():
    """Test event bus functionality."""
    logger.info("=== Testing Event Bus ===")
    
    received_events = []
    
    async def test_handler(event_data):
        received_events.append(event_data)
    
    # Subscribe to event
    await event_bus.subscribe_to_topic("test_event", test_handler)
    
    # Publish event
    await event_bus.publish("test_event", {"test": "data"})
    await asyncio.sleep(0.1)  # Give time for processing
    
    # Check event was received
    assert len(received_events) == 1
    assert received_events[0]["test"] == "data"
    
    # Unsubscribe
    await event_bus.unsubscribe("test_event", test_handler)
    
    # Publish another event
    await event_bus.publish("test_event", {"test": "data2"})
    await asyncio.sleep(0.1)
    
    # Should not have received the second event
    assert len(received_events) == 1
    
    logger.info("✓ Event Bus test passed!")


async def main():
    """Run all tests."""
    logger.info("Starting Actor System Basic Tests...")
    
    try:
        await test_event_bus()
        await test_message_context()
        await test_basic_actor()
        
        logger.info("🎉 All tests passed! Actor system is working correctly.")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        raise
    
    finally:
        # Cleanup
        await message_context_manager.shutdown()


if __name__ == "__main__":
    asyncio.run(main())

"""
Example demonstrating dependency injection patterns for the LLMCallsActor.

This example shows both approaches:
1. Dependency injection (recommended for testing and explicit dependencies)
2. Auto-initialization (simpler for production use)
"""

import asyncio
from typing import Optional
from unittest.mock import Mock

from app.infrastructure.llm.llm_calls_actor import LLMCallsActor
from app.infrastructure.llm.services.llm_service import LLMService, MockLLMProvider
from app.infrastructure.llm.services.token_calculator import TokenCalculator


class MockLLMService(LLMService):
    """Mock LLM service for testing."""
    
    def __init__(self):
        # Don't call super().__init__() to avoid loading real providers
        self.providers = {"mock": MockLLMProvider()}
        self.primary_provider = "mock"
        self.fallback_providers = []
    
    async def generate_response(self, prompt: str, **kwargs):
        """Generate a mock response."""
        return {
            "content": f"Mock response to: {prompt[:50]}...",
            "provider": "mock",
            "tokens_used": 100,
            "tool_calls": [],
            "processing_time": 0.1
        }


class MockTokenCalculator(TokenCalculator):
    """Mock token calculator for testing."""
    
    def estimate_tokens(self, text: str) -> int:
        """Estimate tokens (simple word count for testing)."""
        return len(text.split())
    
    def optimize_prompt_length(self, prompt: str, provider: str, max_response_tokens: int = 1000):
        """Mock optimization that doesn't truncate."""
        return prompt, {
            "was_truncated": False,
            "original_tokens": self.estimate_tokens(prompt),
            "optimized_tokens": self.estimate_tokens(prompt)
        }


async def example_dependency_injection():
    """Example 1: Dependency injection approach (recommended for testing)."""
    print("=== Example 1: Dependency Injection ===")
    
    # Create service instances explicitly
    llm_service = MockLLMService()
    token_calculator = MockTokenCalculator()
    
    # Inject dependencies into the actor
    actor = LLMCallsActor(
        name="TestLLMCallsActor",
        llm_service=llm_service,
        token_calculator=token_calculator
    )
    
    # Initialize and start the actor
    await actor.initialize()
    await actor.start_actor()
    
    print(f"✅ Actor '{actor.name}' initialized with injected dependencies")
    print(f"   - LLM Service: {type(actor.llm_service).__name__}")
    print(f"   - Token Calculator: {type(actor.token_calculator).__name__}")
    print(f"   - Primary Provider: {actor.llm_service.primary_provider}")
    
    # Test the services
    response = await actor.llm_service.generate_response("Test prompt")
    print(f"   - Test response: {response['content']}")
    
    tokens = actor.token_calculator.estimate_tokens("Hello world")
    print(f"   - Token estimation: {tokens} tokens for 'Hello world'")
    
    await actor.stop_actor()
    print("✅ Actor stopped successfully\n")


async def example_auto_initialization():
    """Example 2: Auto-initialization approach (simpler for production)."""
    print("=== Example 2: Auto-initialization ===")
    
    # Create actor without explicit dependencies (they'll be auto-created)
    actor = LLMCallsActor(name="AutoInitLLMCallsActor")
    
    # Initialize and start the actor
    await actor.initialize()
    await actor.start_actor()
    
    print(f"✅ Actor '{actor.name}' initialized with auto-created dependencies")
    print(f"   - LLM Service: {type(actor.llm_service).__name__}")
    print(f"   - Token Calculator: {type(actor.token_calculator).__name__}")
    print(f"   - Primary Provider: {actor.llm_service.primary_provider}")
    print(f"   - Available Providers: {list(actor.llm_service.providers.keys())}")
    
    # Test the services
    try:
        response = await actor.llm_service.generate_response("Test prompt")
        print(f"   - Test response: {response['content'][:100]}...")
    except Exception as e:
        print(f"   - Service test failed (expected in demo): {e}")
    
    await actor.stop_actor()
    print("✅ Actor stopped successfully\n")


async def example_mixed_injection():
    """Example 3: Mixed approach - inject some dependencies, auto-create others."""
    print("=== Example 3: Mixed Injection ===")
    
    # Create only the LLM service, let token calculator be auto-created
    llm_service = MockLLMService()
    
    actor = LLMCallsActor(
        name="MixedLLMCallsActor",
        llm_service=llm_service
        # token_calculator will be auto-created
    )
    
    await actor.initialize()
    await actor.start_actor()
    
    print(f"✅ Actor '{actor.name}' initialized with mixed dependencies")
    print(f"   - LLM Service: {type(actor.llm_service).__name__} (injected)")
    print(f"   - Token Calculator: {type(actor.token_calculator).__name__} (auto-created)")
    
    await actor.stop_actor()
    print("✅ Actor stopped successfully\n")


def demonstrate_type_safety():
    """Demonstrate how dependency injection solves type hint issues."""
    print("=== Type Safety Demonstration ===")
    
    # With dependency injection, type hints are clear and safe
    llm_service: LLMService = MockLLMService()
    token_calculator: TokenCalculator = MockTokenCalculator()
    
    actor = LLMCallsActor(
        llm_service=llm_service,
        token_calculator=token_calculator
    )
    
    # These are guaranteed to be non-None and properly typed
    assert actor.llm_service is not None
    assert actor.token_calculator is not None
    assert isinstance(actor.llm_service, LLMService)
    assert isinstance(actor.token_calculator, TokenCalculator)
    
    print("✅ Type safety verified - no None values, proper types")
    print(f"   - llm_service type: {type(actor.llm_service)}")
    print(f"   - token_calculator type: {type(actor.token_calculator)}")
    print()


async def main():
    """Run all examples."""
    print("🚀 LLMCallsActor Dependency Injection Examples\n")
    
    # Demonstrate type safety first
    demonstrate_type_safety()
    
    # Run async examples
    await example_dependency_injection()
    await example_auto_initialization()
    await example_mixed_injection()
    
    print("📋 Summary:")
    print("1. Dependency injection (Example 1) is recommended for:")
    print("   - Unit testing with mocks")
    print("   - Explicit dependency management")
    print("   - Type safety guarantees")
    print("   - Better testability")
    print()
    print("2. Auto-initialization (Example 2) is good for:")
    print("   - Simple production setups")
    print("   - Rapid prototyping")
    print("   - When you don't need custom service configurations")
    print()
    print("3. Mixed approach (Example 3) offers flexibility:")
    print("   - Inject critical dependencies")
    print("   - Auto-create simple ones")
    print("   - Balance between control and simplicity")


if __name__ == "__main__":
    asyncio.run(main())

# =============================================================================
# ZEITWAHL BOT CONFIGURATION EXAMPLE
# =============================================================================
# Copy this file to .env and fill in your actual values
# Never commit .env file to version control!

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
APP_ENVIRONMENT=development
APP_DEBUG=true
APP_LOG_LEVEL=INFO
APP_RATE_LIMIT_REQUESTS=10
APP_RATE_LIMIT_WINDOW=60
APP_SECRET_KEY=your-super-secret-key-change-this-in-production

# =============================================================================
# TELEGRAM BOT CONFIGURATION
# =============================================================================
# Get your bot token from @BotFather on Telegram
TELEGRAM_BOT_TOKEN=your-telegram-bot-token-here

# Optional: For webhook mode (leave empty for polling)
TELEGRAM_WEBHOOK_URL=
TELEGRAM_WEBHOOK_SECRET=

# =============================================================================
# LLM PROVIDERS CONFIGURATION
# =============================================================================
# Primary LLM provider (gemini, deepseek, openai)
LLM_PRIMARY_PROVIDER=gemini
LLM_FALLBACK_PROVIDERS=["deepseek"]

# LLM General Settings
LLM_MAX_TOKENS=4000
LLM_TEMPERATURE=0.7
LLM_TIMEOUT=30

# Gemini Configuration
# Get API key from: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your-gemini-api-key-here
GEMINI_MODEL=gemini-pro

# Deepseek Configuration
# Get API key from: https://platform.deepseek.com/api_keys
DEEPSEEK_API_KEY=your-deepseek-api-key-here
DEEPSEEK_MODEL=deepseek-chat
DEEPSEEK_BASE_URL=https://api.deepseek.com

# OpenAI Configuration (optional, for testing)
# Get API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-3.5-turbo

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# MongoDB Configuration
DB_MONGODB_URL=mongodb://localhost:27017
DB_DATABASE_NAME=zeitwahl

# Redis Configuration (for caching)
DB_REDIS_URL=redis://localhost:6379
DB_REDIS_DB=0
DB_CACHE_TTL=3600

# =============================================================================
# CALENDAR INTEGRATIONS
# =============================================================================
# Google Calendar Configuration
# Get credentials from: https://console.cloud.google.com/
CALENDAR_GOOGLE_CREDENTIALS_FILE=
CALENDAR_GOOGLE_CLIENT_ID=
CALENDAR_GOOGLE_CLIENT_SECRET=

# Microsoft Outlook Configuration
# Get credentials from: https://portal.azure.com/
CALENDAR_OUTLOOK_CLIENT_ID=
CALENDAR_OUTLOOK_CLIENT_SECRET=
CALENDAR_OUTLOOK_TENANT_ID=

# Default timezone for calendar operations
CALENDAR_DEFAULT_TIMEZONE=UTC

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
# Set to true to enable additional development features
DEV_MODE=true

# =============================================================================
# PRODUCTION SETTINGS
# =============================================================================
# Uncomment and configure for production deployment
# APP_ENVIRONMENT=production
# APP_DEBUG=false
# APP_LOG_LEVEL=WARNING
# APP_SECRET_KEY=your-production-secret-key-here

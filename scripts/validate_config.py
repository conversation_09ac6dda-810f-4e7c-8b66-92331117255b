#!/usr/bin/env python3
"""
Configuration validation script for Zeitwahl bot.

This script helps validate that all required configuration is properly set up.
Run this before starting the bot to ensure everything is configured correctly.
"""

import sys
import os
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.config.settings import settings


def validate_telegram_config():
    """Validate Telegram configuration."""
    print("🔍 Validating Telegram configuration...")
    
    if not settings.telegram.bot_token:
        print("❌ TELEGRAM_BOT_TOKEN is not set")
        return False
    
    if settings.telegram.bot_token == "your-telegram-bot-token-here":
        print("❌ TELEGRAM_BOT_TOKEN is still set to the example value")
        return False
    
    print("✅ Telegram configuration is valid")
    return True


def validate_llm_config():
    """Validate LLM configuration."""
    print("🔍 Validating LLM configuration...")
    
    provider = settings.llm.primary_provider
    print(f"   Primary provider: {provider}")
    
    if provider == "gemini":
        if not settings.llm.gemini_api_key or settings.llm.gemini_api_key == "your-gemini-api-key-here":
            print("❌ Gemini API key is not properly configured")
            return False
        print("✅ Gemini configuration is valid")
    
    elif provider == "deepseek":
        if not settings.llm.deepseek_api_key or settings.llm.deepseek_api_key == "your-deepseek-api-key-here":
            print("❌ Deepseek API key is not properly configured")
            return False
        print("✅ Deepseek configuration is valid")
    
    elif provider == "openai":
        if not settings.llm.openai_api_key or settings.llm.openai_api_key == "your-openai-api-key-here":
            print("❌ OpenAI API key is not properly configured")
            return False
        print("✅ OpenAI configuration is valid")
    
    else:
        print(f"❌ Unknown LLM provider: {provider}")
        return False
    
    return True


def validate_database_config():
    """Validate database configuration."""
    print("🔍 Validating database configuration...")
    
    print(f"   MongoDB URL: {settings.database.mongodb_url}")
    print(f"   Database name: {settings.database.database_name}")
    print(f"   Redis URL: {settings.database.redis_url}")
    
    print("✅ Database configuration looks good")
    return True


def validate_app_config():
    """Validate application configuration."""
    print("🔍 Validating application configuration...")
    
    print(f"   Environment: {settings.app.environment}")
    print(f"   Debug mode: {settings.app.debug}")
    print(f"   Log level: {settings.app.log_level}")
    
    if settings.app.secret_key == "dev-secret-key" and settings.app.environment == "production":
        print("⚠️  WARNING: Using default secret key in production!")
        return False
    
    print("✅ Application configuration is valid")
    return True


def validate_calendar_config():
    """Validate calendar configuration."""
    print("🔍 Validating calendar configuration...")
    
    google_configured = bool(settings.calendar.google_client_id and settings.calendar.google_client_secret)
    outlook_configured = bool(settings.calendar.outlook_client_id and settings.calendar.outlook_client_secret)
    
    if not google_configured and not outlook_configured:
        print("⚠️  No calendar providers configured (this is optional)")
    else:
        if google_configured:
            print("✅ Google Calendar configuration found")
        if outlook_configured:
            print("✅ Outlook Calendar configuration found")
    
    print(f"   Default timezone: {settings.calendar.default_timezone}")
    return True


def main():
    """Main validation function."""
    print("🚀 Zeitwahl Bot Configuration Validator")
    print("=" * 50)
    
    # Check if .env file exists
    env_file = Path(".env")
    if not env_file.exists():
        print("❌ .env file not found!")
        print("   Please copy .env.example to .env and configure your settings")
        return False
    
    print("✅ .env file found")
    print()
    
    # Run all validations
    validations = [
        validate_app_config,
        validate_telegram_config,
        validate_llm_config,
        validate_database_config,
        validate_calendar_config,
    ]
    
    all_valid = True
    for validation in validations:
        try:
            if not validation():
                all_valid = False
        except Exception as e:
            print(f"❌ Error during validation: {e}")
            all_valid = False
        print()
    
    # Final result
    print("=" * 50)
    if all_valid:
        print("🎉 All validations passed! Your bot is ready to run.")
        return True
    else:
        print("❌ Some validations failed. Please fix the issues above.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

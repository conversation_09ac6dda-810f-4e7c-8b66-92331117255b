#!/bin/bash

# <PERSON><PERSON>wahl Bot - Run Script
# This script starts the Zeitwahl AI Agent

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 Starting Zeitwahl AI Agent...${NC}"

# Check if virtual environment exists
if [ ! -d ".venv" ]; then
    echo -e "${RED}❌ Virtual environment not found. Please run setup.sh first.${NC}"
    exit 1
fi

# Activate virtual environment
source .venv/bin/activate

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo -e "${RED}❌ .env file not found. Please copy .env.example to .env and configure it.${NC}"
    exit 1
fi

# Clear any conflicting environment variables that might override .env file
unset DB_MONGODB_URL

# Run the application
echo -e "${YELLOW}📱 Starting bot in polling mode...${NC}"
python -m app.main

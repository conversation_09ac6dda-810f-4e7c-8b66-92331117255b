// Folder-specific settings
//
// For a full list of overridable settings, and general information on folder-specific settings,
// see the documentation: https://zed.dev/docs/configuring-zed#settings-files
{
  "languages": {
    "Python": {
      "language_servers": ["pyright","ruff"]
    }
  },
  "lsp": {
      "pyright": {
        "settings": {
          "python": {
            "pythonPath": ".venv/bin/python"
          }
        }
      }
  },
  "terminal": {
      "detect_venv": {
        "on": {
          // Default directories to search for virtual environments, relative
          // to the current working directory. We recommend overriding this
          // in your project's settings, rather than globally.
          "directories": [".venv"],
          // Can also be `csh`, `fish`, and `nushell`
          "activate_script": "default"
        }
      }
    }
}

# Event-Driven Database Architecture

This document describes the event-driven database architecture implemented for the Zeitwahl AI Agent, which provides isolated database operations through the event bus while maintaining state consistency.

## Overview

The database layer has been reorganized to follow an event-driven architecture that:
- Isolates database operations from business services
- Uses the event bus for all database communication
- Maintains state consistency during async message processing
- Supports both immediate results and async operations

## Architecture Components

### 1. Database Location: `/app/db/`

Database services are now located outside of `/app/services/` to emphasize their isolation:

```
/app/db/
├── __init__.py
├── connection_manager.py          # MongoDB connection management
├── database_initializer.py        # Database and index initialization
├── database_service.py            # Event-driven database service
├── bot_user_repository.py         # BotUser CRUD operations
└── conversation_message_repository.py  # ConversationMessage CRUD operations
```

### 2. Simplified Entity Models

#### BotUser (`/app/utils/models/bot_user.py`)
- **Removed**: `preferences` field (simplified)
- **Kept**: Essential user identification and tracking
- **Key Fields**:
  - `telegram_user_id`: Unique identifier
  - `username`, `first_name`, `last_name`: Profile info
  - `timezone`: User timezone
  - `is_active`, `is_bot`: Status flags
  - `created_at`, `updated_at`, `last_seen_at`: Timestamps
  - `active_chats`, `total_messages`: Activity tracking

#### ConversationMessage (`/app/utils/models/conversation_message.py`)
- **Removed**: User profile fields (only keep `telegram_user_id`)
- **Removed**: `thread_id` (unnecessary)
- **Removed**: Complex processing fields (moved to separate collection)
- **Removed**: Bot response fields (messages organized by arrival time)
- **Key Fields**:
  - `telegram_message_id`, `chat_id`: Message identification
  - `telegram_user_id`: User link (None for bot messages)
  - `message_text`, `message_type`: Content
  - `is_from_bot`: Message source flag
  - `telegram_timestamp`, `received_at`: Timing
  - `is_reply`, `reply_to_message_id`: Reply context
  - `processing_status`: Simple status tracking
  - `chat_type`, `chat_title`: Chat context

### 3. Event-Driven Communication

#### Database Events (`/app/utils/database_events.py`)

**Request/Response Pattern**:
- All database operations use request/response events
- Each request has a unique `request_id` for correlation
- Responses include success status and error handling

**Key Event Types**:
- `CreateUserRequest/Response`
- `GetUserByTelegramIdRequest/Response`
- `UpdateUserLastSeenRequest/Response`
- `IncrementUserMessageCountRequest/Response`
- `CreateMessageRequest/Response`
- `GetConversationHistoryRequest/Response`
- `InitializeDatabaseRequest/Response`
- `DatabaseHealthCheckRequest/Response`

#### Database Service (`/app/db/database_service.py`)

Event-driven service that:
- Subscribes to all database request events
- Performs operations through repositories
- Publishes response events
- Tracks operation metrics and timing
- Handles errors gracefully

### 4. State Management

#### DatabaseStateManager (`/app/utils/database_state_manager.py`)

Maintains state consistency during async operations:
- **Request Correlation**: Matches requests with responses using `request_id`
- **Timeout Management**: Handles request timeouts and cleanup
- **Future-based Waiting**: Provides synchronous-like interface for async operations
- **Error Handling**: Propagates database errors to calling code

#### DatabaseClient (`/app/utils/database_state_manager.py`)

High-level client interface:
- `get_user_by_telegram_id(telegram_user_id)`
- `create_user(user)`
- `create_message(message)`
- `get_conversation_history(chat_id, telegram_user_id, limit)`
- `update_user_last_seen(telegram_user_id)`
- `increment_user_message_count(telegram_user_id)`

### 5. Integration with Preprocessing

#### Updated UserIdentifier (`/app/preprocess/user_identifier.py`)

Now uses the database client instead of in-memory storage:
- `identify_user()`: Gets/creates users via database events
- `get_conversation_history()`: Retrieves from database
- `add_to_conversation_history()`: Stores messages in database
- Maintains small cache for frequently accessed data

## Usage Examples

### Basic User Operations

```python
from app.utils.database_state_manager import database_client
from app.common.models import BotUser

# Create user
user = BotUser(
    telegram_user_id=123456789,
    username="john_doe",
    first_name="John"
)
created_user = await database_client.create_user(user)

# Get user
user = await database_client.get_user_by_telegram_id(123456789)

# Update activity
await database_client.update_user_last_seen(123456789)
await database_client.increment_user_message_count(123456789)
```

### Message Operations

```python
from app.common.models import ConversationMessage

# Create user message
message = ConversationMessage.from_telegram_message(telegram_data, chat_id)
created_message = await database_client.create_message(message)

# Create bot message
bot_message = ConversationMessage.create_bot_message(
    chat_id=chat_id,
    message_text="Hello! I'm a bot response.",
    reply_to_message_id=user_message_id
)
await database_client.create_message(bot_message)

# Get conversation history
history = await database_client.get_conversation_history(
    chat_id=chat_id,
    telegram_user_id=user_id,
    limit=50
)
```

### Database Initialization

```python
from app.db import database_service

# Initialize database service
await database_service.initialize()

# Database will automatically:
# - Connect to MongoDB Atlas
# - Create collections
# - Set up optimized indexes
# - Subscribe to events
```

## Benefits

### 1. **Isolation and Decoupling**
- Database operations are completely isolated from business logic
- Services communicate only through events
- Easy to test and mock database operations

### 2. **State Consistency**
- DatabaseStateManager ensures consistent state during async operations
- Request/response correlation prevents race conditions
- Timeout handling prevents hanging operations

### 3. **Scalability**
- Event-driven architecture supports horizontal scaling
- Database operations can be moved to separate processes/services
- Connection pooling and proper resource management

### 4. **Maintainability**
- Clear separation of concerns
- Simplified entity models
- Comprehensive error handling and logging

### 5. **Performance**
- Optimized indexes for common query patterns
- Connection pooling for efficient resource usage
- Async operations don't block message processing

## Event Bus Integration

The database layer integrates seamlessly with the existing event bus:

```python
# Preprocessing can use database events
@event_bus.subscribe("MessageReceived")
async def handle_message(event: MessageReceived):
    # Get user (maintains state consistency)
    user = await database_client.get_user_by_telegram_id(event.user_id)
    
    # Store message
    message = ConversationMessage.from_telegram_message(event.telegram_data, event.chat_id)
    await database_client.create_message(message)
    
    # Continue processing...
```

## Testing

All entity models and database operations are thoroughly tested:

```bash
# Run database entity tests
python -m pytest tests/test_database_entities.py -v

# Run example usage
python examples/database_usage_example.py
```

## Configuration

Database configuration is managed through settings:

```python
# app/config/settings.py
class DatabaseConfig(BaseSettings):
    mongodb_url: str = Field(default="mongodb://localhost:27017")
    database_name: str = Field(default="zeitwahl")
```

Environment variables:
```bash
DB_MONGODB_URL=mongodb+srv://user:<EMAIL>/
DB_DATABASE_NAME=zeitwahl
```

This event-driven database architecture provides a robust, scalable foundation for the Zeitwahl AI Agent while maintaining the simplicity and consistency required for reliable message processing.

# Database Entities and Services Guide

This guide explains the MongoDB entities and services implemented for the Zeitwahl AI Agent, including BotUser and ConversationMessage models with their corresponding repositories.

## Overview

The database layer follows the user's preferred architecture with:
- Entity models in `/app/utils/models/`
- Repository services in `/app/services/`
- MongoDB Atlas cloud integration
- Proper indexing for efficient queries
- Event bus integration ready

## Entity Models

### BotUser (`app/utils/models/bot_user.py`)

Represents a Telegram user with all necessary information for unique identification.

**Key Fields:**
- `telegram_user_id`: Unique Telegram user ID (primary identifier)
- `username`, `first_name`, `last_name`: User profile information
- `language_code`: User's language preference
- `timezone`: User's timezone setting
- `preferences`: User preferences and settings (working hours, notifications, etc.)
- `is_active`, `is_bot`: User status flags
- `created_at`, `updated_at`, `last_seen_at`: Timestamp tracking
- `active_chats`: List of chat IDs where user is active
- `total_messages`: Message count for analytics
- `metadata`: Additional user metadata

**Key Methods:**
- `update_last_seen()`: Update last activity timestamp
- `add_chat(chat_id)`: Add chat to active chats
- `increment_message_count()`: Increment message counter
- `update_profile()`: Update user profile information
- `get_display_name()`: Get user's display name
- `to_dict()` / `from_dict()`: Serialization for MongoDB

### ConversationMessage (`app/utils/models/conversation_message.py`)

Represents a message in a conversation with linking information and processing status.

**Key Fields:**
- `telegram_message_id`, `chat_id`: Unique message identification
- `telegram_user_id`: Links message to user
- `message_text`, `message_type`: Message content and type
- `username`, `first_name`, `last_name`: Sender information
- `telegram_timestamp`, `received_at`, `processed_at`: Timing information
- `is_reply`, `reply_to_message_id`, `thread_id`: Conversation threading
- `processing_status`: Message processing state (received, processing, processed, error)
- `bot_response`, `bot_response_timestamp`: Bot response information
- `llm_provider_used`, `tokens_used`, `tools_executed`: LLM processing details
- `chat_type`, `chat_title`: Chat context information
- `error_message`, `retry_count`: Error handling

**Key Methods:**
- `mark_as_processing()`: Mark message as being processed
- `mark_as_processed()`: Mark message as processed with response details
- `mark_as_error()`: Mark message as having processing error
- `set_reply_context()`: Set reply threading information
- `get_conversation_key()`: Get unique conversation identifier
- `get_sender_display_name()`: Get sender's display name
- `from_telegram_message()`: Create from Telegram message data

## Repository Services

### BotUserRepository (`app/services/bot_user_repository.py`)

Provides CRUD operations for BotUser entities.

**Key Methods:**
- `create_user(user)`: Create new user
- `get_user_by_telegram_id(telegram_user_id)`: Get user by Telegram ID
- `get_user_by_id(user_id)`: Get user by MongoDB ObjectId
- `update_user(user)`: Update existing user
- `delete_user(telegram_user_id)`: Delete user
- `get_active_users(limit)`: Get list of active users
- `update_last_seen(telegram_user_id)`: Update last seen timestamp
- `increment_message_count(telegram_user_id)`: Increment message count

### ConversationMessageRepository (`app/services/conversation_message_repository.py`)

Provides CRUD operations for ConversationMessage entities.

**Key Methods:**
- `create_message(message)`: Create new message
- `get_message_by_telegram_id(telegram_message_id, chat_id)`: Get message by Telegram ID
- `get_message_by_id(message_id)`: Get message by MongoDB ObjectId
- `update_message(message)`: Update existing message
- `get_conversation_history(chat_id, telegram_user_id, limit, offset)`: Get conversation history
- `get_recent_messages(chat_id, hours, limit)`: Get recent messages in chat
- `get_unprocessed_messages(limit)`: Get unprocessed messages
- `mark_message_as_processed()`: Mark message as processed with details
- `delete_old_messages(days)`: Clean up old messages

## Database Services

### DatabaseService (`app/services/db_service.py`)

Centralized MongoDB connection and management service.

**Features:**
- Connection management with proper configuration
- Health checking and monitoring
- Database and collection access
- Error handling and logging
- Connection pooling and timeouts

### DatabaseInitializer (`app/services/db_initializer.py`)

Database initialization with proper indexing for efficient queries.

**Indexes Created:**

**BotUser Collection:**
- Unique index on `telegram_user_id`
- Indexes on `username`, `is_active`, `last_seen_at`, `created_at`
- Compound index for active users by last seen
- Text search index for user names

**ConversationMessage Collection:**
- Unique compound index on `telegram_message_id` + `chat_id`
- Indexes on `chat_id`, `telegram_user_id`, `telegram_timestamp`
- Indexes on `processing_status`, `received_at`
- Compound indexes for conversation history and recent messages
- Text search index for message content
- Indexes for threading (`thread_id`, `reply_to_message_id`)

## Usage Examples

### Basic User Operations

```python
from app.services import BotUserRepository
from app.common.models import BotUser

# Create repository
user_repo = BotUserRepository()

# Create new user
user = BotUser(
    telegram_user_id=123456789,
    username="john_doe",
    first_name="John",
    last_name="Doe"
)
created_user = await user_repo.create_user(user)

# Get user
user = await user_repo.get_user_by_telegram_id(123456789)

# Update activity
await user_repo.update_last_seen(123456789)
await user_repo.increment_message_count(123456789)
```

### Basic Message Operations

```python
from app.services import ConversationMessageRepository
from app.common.models import ConversationMessage

# Create repository
message_repo = ConversationMessageRepository()

# Create message from Telegram data
message = ConversationMessage.from_telegram_message(telegram_message, chat_id)
created_message = await message_repo.create_message(message)

# Mark as processed
await message_repo.mark_message_as_processed(
    message.id,
    bot_response="Hello! I received your message.",
    llm_provider="gemini",
    tokens_used=25
)

# Get conversation history
history = await message_repo.get_conversation_history(
    chat_id=chat_id,
    telegram_user_id=user_id,
    limit=50
)
```

### Database Initialization

```python
from app.services import db_service, db_initializer

# Connect and initialize
await db_service.connect()
init_results = await db_initializer.initialize_database()
```

## Configuration

The database services use configuration from `app/config/settings.py`:

```python
class DatabaseConfig(BaseSettings):
    mongodb_url: str = Field(default="mongodb://localhost:27017")
    database_name: str = Field(default="zeitwahl")
    # ... other settings
```

Set environment variables:
```bash
DB_MONGODB_URL=mongodb+srv://user:<EMAIL>/
DB_DATABASE_NAME=zeitwahl
```

## Testing

Run the entity tests:
```bash
python -m pytest tests/test_database_entities.py -v
```

Run the example usage:
```bash
python examples/database_usage_example.py
```

## Integration with Event Bus

The repositories are designed to work with the existing event bus system. You can subscribe to database events and publish events when entities are created or updated.

Example integration:
```python
from app.utils.event_bus import event_bus
from app.utils.events import UserSessionStarted

class UserEventHandler:
    @event_bus.subscribe("UserSessionStarted")
    async def handle_user_session(self, event: UserSessionStarted):
        user_repo = BotUserRepository()
        await user_repo.update_last_seen(event.user_id)
```

## Best Practices

1. **Always use repositories** for database operations instead of direct MongoDB calls
2. **Handle exceptions** properly - repositories raise specific exceptions for different error conditions
3. **Use proper indexing** - the initializer sets up optimal indexes for common query patterns
4. **Monitor performance** - use the health check methods to monitor database performance
5. **Clean up old data** - use the cleanup methods to manage database size
6. **Follow the event bus pattern** - integrate database operations with the event system

This implementation provides a robust, scalable foundation for user and message management in the Zeitwahl AI Agent system.

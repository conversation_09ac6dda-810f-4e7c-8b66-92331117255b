# Zeitwahl AI Agent - Event-Driven Architecture

This document describes the new event-driven architecture for the Zeitwahl AI agent system, designed following KISS principles with clear separation of concerns and decoupled components.

## Architecture Overview

The system is built around an event bus that enables loose coupling between components. Each component publishes and subscribes to events, creating a reactive system that's easy to test, maintain, and extend.

## Core Components

### 1. Event Bus (`app/utils/event_bus.py`)

The heart of the system, providing:
- **Decorator-based subscriptions**: `@event_bus.subscribe("EventName")`
- **Tagged method auto-discovery**: Automatically finds and subscribes methods
- **Priority-based ordering**: Higher priority subscribers execute first
- **Error isolation**: Failures in one handler don't affect others
- **Event registry**: Complete audit trail of all events

```python
class MyHandler:
    @eb.subscribe("MessageReceived")
    async def handle_message(self, event):
        # Process the event
        pass

# Auto-subscribe all tagged methods
handler = MyHandler()
event_bus.subscribe_tagged_methods(handler)
```

### 2. Events (`app/utils/events.py`)

Structured event definitions for:
- `MessageReceived`
- `MessagePreprocessed`
- `LLMResponseReceived`
- `ToolExecutionStarted`
- `ToolExecutionCompleted`
- `ResponseReady`
- `ErrorOccurred`
- `UserSessionStarted`
- `UserSessionEnded`
- `SystemHealthCheck`

### 3. Bot (`app/bot/`)

A Telegram bot (using aiogram) that:
- Receives messages and commands
- Identifies user from message
- Reponds statically to bot commands like `/start` and `/help` etc.
- Do a minimal preprocessing of messages
- Publishes `MessageReceived` events if message is not a command
- Integrates with event bus for extensibility
- Sends message received on `ResponseReady` events


### 4. LLM Preprocessing Pipeline (`app/preprocess/`)

Transforms raw messages into structured prompts:

#### Message Validator (`message_validator.py`)
- Input sanitization and validation
- Rate limiting and spam detection
- Content filtering and safety checks

#### User Identifier (`user_identifier.py`)
- User recognition and 
- Context loading (conversation history) from mongodb or cache (redis) if available
- Load user available integrations (Google Calendar, Outlook Calendar) information available in the database.

#### Context Builder (`context_builder.py`)
- Conversation history aggregation
- External API data integration (calendar, weather)
- Temporal context (time, date, timezone)

#### Prompt Builder (`prompt_builder.py`)
- Structured prompt construction
- Tool definition inclusion (based on available integrations also)
- Context optimization and truncation

#### Preprocessor (`preprocessor.py`)
- Orchestrates the entire preprocessing pipeline
- Publishes `MessagePreprocessed` events

### 5. LLM Service (`app/services/llm_service.py`)

Manages multiple LLM providers with:
- **Multi-provider support**: Gemini, Deepseek, local models for testing
- **Automatic failover**: Switches providers on failure
- **Response streaming**: Real-time response generation
- **Token optimization**: Efficient prompt and response handling

### 6. Postprocessing Pipeline (`app/postprocess/`)

Processes LLM responses and executes actions:

#### Response Validator (`response_validator.py`)
- Content safety and appropriateness checks
- Format and structure validation
- Quality assessment and scoring

#### Tool Executor (`tool_executor.py`)
- Tool execution though external API calls
- Parallel and sequential execution modes

#### Postprocessor (`postprocessor.py`)
- Orchestrates the postprocessing pipeline
- Builds the reponse message from llm + tools responses
- Publishes `ResponseReady` events

### 7. Services (`app/services/`)

Business logic and external integrations:

#### Calendar Service (`calendar_service.py`)
- Multi-provider calendar support (Google, Outlook, local)
- Event CRUD operations
- Conflict detection and resolution

#### User Service (`user_service.py`)
- Enhanced user management
- Session tracking and analytics
- Preference management

### 8. Configuration (`app/config/`)

Comprehensive configuration management:
- Environment-based settings
- Component-specific configurations
- Validation and type checking
- Secret management
- Static prompts

## Event Flow

1. **Message Reception**: Bot handler receives Telegram message
2. **Message PreValidation**: Identifies user
2. **Event Publication**: `MessageReceived` event published to event bus
3. **Preprocessing**: Preprocessor validates, builds context and prompt with conversation and existing events retriaval, tools injection on the prompt (JSON format)
4. **LLM Processing**: LLM service generates response containing actions to do
5. **Postprocessing**: Response validated, quality evaluated, tools executed, reponse message created
6. **Response Delivery**: Final response sent back to user

## Key Features

### Event-Driven Architecture
- **Loose Coupling**: Components communicate only through events
- **Scalability**: Easy to add new components and handlers
- **Testability**: Each component can be tested in isolation
- **Monitoring**: Leave this part for now

### KISS/SOLID/DRY Principle Adherence
- **Simple Interfaces**: Clear, focused component responsibilities
- **Minimal Dependencies**: Each component has minimal external dependencies
- **Easy Configuration**: Environment-based configuration with sensible defaults
- **Straightforward Testing**: Simple unit and integration testing (pytest)

### Extensibility
- **Plugin Architecture**: Easy to add new LLM providers, tools, or services
- **Event Middleware**: Add cross-cutting concerns like logging, metrics
- **Service Discovery**: Automatic registration of tagged methods

## Usage Examples

### Basic Event Handling
```python
from app.utils.event_bus import event_bus
from app.utils.events import MessageReceived

class MyHandler:
    @eb.subscribe("MessageReceived")
    async def handle_message(self, event: MessageReceived):
        print(f"Received: {event.message_text}")

# Register handler
handler = MyHandler()
eb.subscribe_tagged_methods(handler)

# Publish event
await eb.publish(MessageReceived(
    user_id=123,
    chat_id=456,
    message_text="Hello, world!",
    # ... other fields
))
```

### Service Integration
```python
from app.services.calendar_service import CalendarService

# Initialize service
calendar = CalendarService({
    "providers": {"google": {"api_key": "your-key"}},
    "default_timezone": "UTC"
})

# Create event
event = await calendar.create_event(
    title="Meeting",
    start_time=datetime.now(),
    description="Important meeting"
)
```

## Running the System

### Development
```bash
# Set environment variables
export TELEGRAM_BOT_TOKEN="your-bot-token"
export OPENAI_API_KEY="your-openai-key"

# Run the demo
python example_usage.py

# Run the full application
python -m app.main
```

### Production
```bash
# Set production environment
export ENVIRONMENT=production
export DB_HOST=your-db-host
export DB_PASSWORD=your-db-password

# Run with proper logging
python -m app.main
```

# Zeitwahl AI Agent 🗓️

An intelligent, event-driven calendar assistant built with Python that helps users manage their time, schedule meetings, and organize calendar events through natural language conversations via Telegram.

## Features

- **Event-Driven Architecture**: Loosely coupled components communicating through an optimized event bus
- **Multi-Provider LLM Support**: Gemini, Deepseek, and mock providers with automatic failover
- **Calendar Integration**: Support for Google Calendar, Outlook, and mock providers
- **Natural Language Processing**: Intelligent message preprocessing and context building
- **Smart Scheduling**: Conflict detection, available time slot finding, and working hours respect
- **User Management**: Session tracking, analytics, and preference management
- **Robust Error Handling**: Comprehensive validation, rate limiting, and graceful error recovery
- **Comprehensive Testing**: pytest-based unit tests with realistic failure scenarios

## Architecture

The system follows KISS principles with clear separation of concerns and improved nested folder structure for better navigation:

```
app/
├── core/                    # Core system components
│   ├── actors/             # Actor system and base actor
│   │   ├── actor.py        # Base Actor class
│   │   └── actor_system.py # Actor system manager
│   ├── events/             # Event bus and event definitions
│   │   ├── event_bus.py    # Event bus implementation
│   │   └── events.py       # Event class definitions
│   └── context/            # Message context management
│       └── message_context.py # Message context tracking
├── infrastructure/          # Infrastructure and external integrations
│   ├── database/           # Database layer (MongoDB)
│   │   ├── models/         # Database entity models
│   │   │   ├── bot_user.py
│   │   │   └── conversation_message.py
│   │   ├── repositories/   # Data access repositories
│   │   │   ├── bot_user_repository.py
│   │   │   └── conversation_message_repository.py
│   │   ├── services/       # Database business logic
│   │   │   └── database_service.py
│   │   ├── connection_manager.py
│   │   ├── interfaces.py
│   │   └── db_actor.py     # Database actor
│   ├── telegram/           # Telegram bot integration
│   │   ├── handlers/       # Message and command handlers (future)
│   │   ├── services/       # Bot-related services (future)
│   │   └── bot_actor.py    # Telegram bot actor
│   └── llm/               # LLM API integrations
│       ├── providers/      # Different LLM providers (future)
│       ├── services/       # LLM business logic
│       │   ├── llm_service.py
│       │   └── token_calculator.py
│       └── llm_calls_actor.py # LLM calls actor
├── domain/                 # Business domain logic
│   ├── preprocessing/      # Message preprocessing pipeline
│   │   ├── services/       # Preprocessing services
│   │   │   ├── context_grabber_service.py
│   │   │   ├── prompt_builder_service.py
│   │   │   └── validation_service.py
│   │   └── preprocessing_actor.py
│   ├── postprocessing/     # Response postprocessing pipeline (future)
│   │   └── services/       # Postprocessing services
│   └── scheduling/         # Calendar and scheduling logic
│       └── services/       # Scheduling services
│           ├── calendar_service.py
│           └── user_service.py
├── config/                 # Configuration management
│   ├── settings/           # Environment settings
│   │   └── settings.py
│   └── prompts/            # System prompts
│       └── prompts.py
├── utils/                  # Shared utilities and helpers
│   ├── logging/            # Custom logging utilities
│   │   └── logging_config.py
│   ├── constants/          # Application constants (future)
│   ├── database_events.py  # Database event definitions
│   └── database_state_manager.py
├── bot/                    # Backward compatibility (imports from infrastructure)
├── db/                     # Backward compatibility (imports from infrastructure)
├── preprocess/             # Backward compatibility (imports from domain)
├── services/               # Backward compatibility (imports from domain)
├── llmapi/                 # Backward compatibility (imports from infrastructure)
└── main.py                 # Application entry point
```

### Key Improvements

1. **Better Organization**: Components are now organized by their architectural layer (core, infrastructure, domain)
2. **Nested Structure**: More nested folders for better navigation and readability
3. **Clear Separation**: Infrastructure concerns separated from business logic
4. **Backward Compatibility**: Old import paths still work through forwarding imports
5. **Context Integration**: Message context tracking with `context_id` is now integrated throughout all actors

### Event Flow with Context Tracking

1. **Message Reception**: Telegram bot receives user message → Creates `MessageContext` with unique `context_id`
2. **Preprocessing**: Message validation → User identification → Context building → Prompt construction (all tracked via `context_id`)
3. **LLM Processing**: Multi-provider LLM generates response with tool calls (context state updated throughout)
4. **Postprocessing**: Response validation → Tool execution → Final response assembly (future enhancement)
5. **Response Delivery**: Formatted response sent back to user (final context state update)

### Context ID Integration

The system now uses a comprehensive context tracking mechanism:

- **MessageContext**: Each user message gets a unique `context_id` for end-to-end tracking
- **State Management**: Message processing state is tracked across all actors
- **Event Propagation**: All events now include `context_id` for proper correlation
- **Actor Coordination**: Actors use context to maintain consistency and share state
- **Debugging**: Context tracking enables better debugging and monitoring of message flow

## Quick Start

### Prerequisites

- Python 3.13+
- Telegram Bot Token (from @BotFather)
- Optional: LLM API keys (Gemini, Deepseek)
- Optional: Calendar API credentials (Google, Outlook)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd zeitwahl
```

2. Install dependencies:
```bash
pip install -e .
```

3. Set environment variables:
```bash
export TELEGRAM_BOT_TOKEN="your-telegram-bot-token"
export GEMINI_API_KEY="your-gemini-api-key"  # Optional
export DEEPSEEK_API_KEY="your-deepseek-api-key"  # Optional
```

### Running the Application

#### Development Mode
```bash
# Run the example demonstration (no real bot token needed)
python example_usage.py

# Run the full application
python -m app.main
```

#### Production Mode
```bash
export ENVIRONMENT=production
export LOG_LEVEL=INFO
python -m app.main
```

### Testing

Run the comprehensive test suite:
```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=app

# Run specific test file
pytest tests/test_event_bus.py -v
```

## Configuration

The system uses environment-based configuration with sensible defaults:

### Required Settings
- `TELEGRAM_BOT_TOKEN`: Your Telegram bot token

### Optional Settings
- `LLM_PRIMARY_PROVIDER`: Primary LLM provider (default: "gemini")
- `GEMINI_API_KEY`: Google Gemini API key
- `DEEPSEEK_API_KEY`: Deepseek API key
- `GOOGLE_CLIENT_ID`: Google Calendar integration
- `OUTLOOK_CLIENT_ID`: Outlook Calendar integration
- `LOG_LEVEL`: Logging level (default: "INFO")
- `ENVIRONMENT`: Environment (development/staging/production)

## Usage Examples

### Basic Calendar Operations

```python
# The bot understands natural language:
"Schedule a meeting tomorrow at 2 PM"
"What's on my calendar today?"
"Find me a free hour this week"
"Cancel my 3 PM meeting"
```

### Advanced Features

```python
# Multi-participant scheduling
"Schedule a team <NAME_EMAIL> and <EMAIL> for next Tuesday"

# Recurring events
"Set up a weekly standup every Monday at 9 AM"

# Conflict resolution
"Move my 2 PM meeting to avoid the conflict"
```

## Development

### Adding New LLM Providers

1. Create a new provider class inheriting from `LLMProvider`
2. Implement required methods: `generate_response()`, `stream_response()`
3. Register in `LLMService._initialize_providers()`

### Adding New Calendar Providers

1. Create a new provider class inheriting from `CalendarProvider`
2. Implement CRUD operations for calendar events
3. Register in `CalendarService._initialize_providers()`

### Event-Driven Development

```python
from app.utils.event_bus import event_bus
from app.utils.events import MessageReceived

class MyHandler:
    @event_bus.subscribe(MessageReceived)
    async def handle_message(self, event: MessageReceived):
        print(f"Received: {event.message_text}")

# Register handler
handler = MyHandler()
await event_bus.subscribe_tagged_methods(handler)
```

## Contributing

1. Follow KISS principles - keep it simple and minimal
2. Write tests for new features (pytest)
3. Use the event bus for component communication
4. Add proper error handling and logging
5. Update documentation for new features

## License

[Add your license here]

## Support

For questions and support, please [add contact information or issue tracker link].
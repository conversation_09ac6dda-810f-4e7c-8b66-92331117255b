"""
Validation Service for message validation and pre-filtering.

This service validates incoming messages and applies pre-filters to reduce
load on the LLM by handling simple cases and irrelevant messages.
"""

import logging
import re
from typing import Dict, Any, Optional, List, Tuple

logger = logging.getLogger(__name__)


class ValidationService:
    """Service for validating and pre-filtering messages."""

    def __init__(self):
        # High-priority scheduling keywords (strong indicators)
        self.high_priority_keywords = {
            "schedule", "calendar", "meeting", "appointment", "book", "reserve",
            "plan", "organize", "remind", "reminder", "deadline", "due", "event",
            "reschedule", "cancel", "postpone", "move", "shift", "timezone"
        }

        # Medium-priority time-related keywords
        self.medium_priority_keywords = {
            "time", "date", "when", "tomorrow", "today", "yesterday",
            "next week", "last week", "this week", "next month", "last month",
            "clock", "hour", "minute", "am", "pm", "busy", "free", "available"
        }

        # Day and month keywords
        self.temporal_keywords = {
            "monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday",
            "january", "february", "march", "april", "may", "june",
            "july", "august", "september", "october", "november", "december",
            "jan", "feb", "mar", "apr", "may", "jun", "jul", "aug", "sep", "oct", "nov", "dec"
        }

        # All scheduling-related keywords combined
        self.scheduling_keywords = self.high_priority_keywords | self.medium_priority_keywords | self.temporal_keywords
        
        # Keywords that strongly indicate irrelevant content
        self.irrelevant_keywords = {
            "weather", "joke", "funny", "meme", "game", "play", "gaming",
            "music", "song", "movie", "film", "tv", "show", "entertainment",
            "food", "recipe", "cook", "restaurant", "eat", "cooking",
            "sports", "football", "basketball", "soccer", "tennis", "baseball",
            "politics", "news", "celebrity", "gossip", "drama",
            "shopping", "buy", "purchase", "price", "cost", "money",
            "love", "relationship", "dating", "romance",
            "health", "medical", "doctor", "medicine", "sick"
        }

        # Contextual keywords that could be scheduling-related depending on context
        self.contextual_keywords = {
            "travel", "vacation", "holiday", "trip", "flight", "hotel",
            "work", "office", "business", "conference", "workshop"
        }

        # Intent patterns for quick classification
        self.intent_patterns = {
            "scheduling": [
                r"schedule\s+(a|an|the)?\s*\w+",
                r"book\s+(a|an|the)?\s*\w+",
                r"plan\s+(a|an|the)?\s*\w+",
                r"set\s+up\s+(a|an|the)?\s*\w+",
                r"arrange\s+(a|an|the)?\s*\w+",
                r"when\s+(is|are|can|could|should|would)",
                r"what\s+time",
                r"at\s+\d{1,2}(:\d{2})?\s*(am|pm)?",
                r"(next|this|last)\s+(week|month|year|monday|tuesday|wednesday|thursday|friday|saturday|sunday)"
            ],
            "time_query": [
                r"what\s+(time|date)",
                r"when\s+(is|are|was|were)",
                r"how\s+long",
                r"(before|after|during)\s+\w+"
            ],
            "availability": [
                r"(am|are)\s+(you\s+)?(free|available|busy)",
                r"(can|could)\s+(you|we)\s+(meet|talk|call)",
                r"(do|does)\s+\w+\s+have\s+(time|availability)"
            ]
        }
        
        # Common greetings and simple responses
        self.simple_patterns = {
            "greeting": [
                r"^(hi|hello|hey|good morning|good afternoon|good evening)!?$",
                r"^(how are you|what's up|sup)[\?!]*$"
            ],
            "thanks": [
                r"^(thanks|thank you|thx|ty)!?$",
                r"^(ok|okay|alright|got it)!?$"
            ],
            "goodbye": [
                r"^(bye|goodbye|see you|cya|later)!?$",
                r"^(good night|goodnight)!?$"
            ]
        }
    
    async def validate_and_classify_message(self, message_text: str) -> Dict[str, Any]:
        """
        Validate and classify a message to determine how it should be processed.
        
        Args:
            message_text: The message text to validate
            
        Returns:
            Dictionary containing validation results and classification
        """
        try:
            # Clean and normalize the message
            cleaned_message = self._clean_message(message_text)
            
            # Check if message is empty or too short
            if len(cleaned_message.strip()) < 2:
                return {
                    "is_valid": False,
                    "classification": "empty",
                    "needs_llm": False,
                    "suggested_response": "I didn't receive a clear message. How can I help you with scheduling or time management?",
                    "confidence": 1.0
                }
            
            # Check for simple patterns that don't need LLM
            simple_response = self._check_simple_patterns(cleaned_message)
            if simple_response:
                return {
                    "is_valid": True,
                    "classification": simple_response["type"],
                    "needs_llm": False,
                    "suggested_response": simple_response["response"],
                    "confidence": simple_response["confidence"]
                }
            
            # Check relevance to scheduling/time management
            relevance_score = self._calculate_relevance_score(cleaned_message)
            
            if relevance_score < 0.3:
                return {
                    "is_valid": True,
                    "classification": "irrelevant",
                    "needs_llm": False,
                    "suggested_response": self._get_irrelevant_response(),
                    "confidence": 1.0 - relevance_score,
                    "relevance_score": relevance_score
                }
            
            # Message seems relevant, needs LLM processing
            return {
                "is_valid": True,
                "classification": "relevant",
                "needs_llm": True,
                "suggested_response": None,
                "confidence": relevance_score,
                "relevance_score": relevance_score
            }
            
        except Exception as e:
            logger.error(f"Error validating message: {e}")
            return {
                "is_valid": True,
                "classification": "error",
                "needs_llm": True,
                "suggested_response": None,
                "confidence": 0.0,
                "error": str(e)
            }
    
    def _clean_message(self, message_text: str) -> str:
        """Clean and normalize the message text."""
        # Remove extra whitespace
        cleaned = re.sub(r'\s+', ' ', message_text.strip())
        
        # Convert to lowercase for analysis
        return cleaned.lower()
    
    def _check_simple_patterns(self, message: str) -> Optional[Dict[str, Any]]:
        """Check if the message matches simple patterns that don't need LLM."""
        for pattern_type, patterns in self.simple_patterns.items():
            for pattern in patterns:
                if re.match(pattern, message, re.IGNORECASE):
                    response = self._get_simple_response(pattern_type)
                    return {
                        "type": pattern_type,
                        "response": response,
                        "confidence": 0.9
                    }
        return None
    
    def _get_simple_response(self, pattern_type: str) -> str:
        """Get appropriate response for simple patterns."""
        responses = {
            "greeting": "Hello! I'm Zeitwahl, your AI scheduling assistant. How can I help you with your calendar or time management today?",
            "thanks": "You're welcome! Is there anything else I can help you with regarding scheduling or time management?",
            "goodbye": "Goodbye! Feel free to reach out anytime you need help with scheduling or time management."
        }
        return responses.get(pattern_type, "How can I help you with scheduling or time management?")
    
    def _calculate_relevance_score(self, message: str) -> float:
        """Calculate how relevant the message is to scheduling/time management."""
        words = message.split()
        total_words = len(words)

        if total_words == 0:
            return 0.0

        score = 0.0

        # Check for intent patterns (highest weight)
        intent_score = self._check_intent_patterns(message)
        score += intent_score * 0.4

        # Count high-priority keywords
        high_priority_matches = sum(1 for word in words if any(keyword in word.lower() for keyword in self.high_priority_keywords))
        score += (high_priority_matches / total_words) * 0.3

        # Count medium-priority keywords
        medium_priority_matches = sum(1 for word in words if any(keyword in word.lower() for keyword in self.medium_priority_keywords))
        score += (medium_priority_matches / total_words) * 0.2

        # Count temporal keywords
        temporal_matches = sum(1 for word in words if any(keyword in word.lower() for keyword in self.temporal_keywords))
        score += (temporal_matches / total_words) * 0.15

        # Check for time patterns (dates, times, etc.)
        time_patterns = self._count_time_patterns(message)
        score += min(time_patterns * 0.1, 0.3)  # Cap at 0.3

        # Count irrelevant keywords (negative score)
        irrelevant_matches = sum(1 for word in words if any(keyword in word.lower() for keyword in self.irrelevant_keywords))
        irrelevant_penalty = (irrelevant_matches / total_words) * 0.4
        score -= irrelevant_penalty

        # Check contextual keywords (neutral to slightly positive)
        contextual_matches = sum(1 for word in words if any(keyword in word.lower() for keyword in self.contextual_keywords))
        if contextual_matches > 0 and irrelevant_matches == 0:
            score += (contextual_matches / total_words) * 0.1

        # Ensure score is between 0 and 1
        return max(0.0, min(1.0, score))

    def _check_intent_patterns(self, message: str) -> float:
        """Check for intent patterns and return confidence score."""
        max_score = 0.0

        for intent_type, patterns in self.intent_patterns.items():
            for pattern in patterns:
                if re.search(pattern, message, re.IGNORECASE):
                    if intent_type == "scheduling":
                        max_score = max(max_score, 0.9)
                    elif intent_type == "time_query":
                        max_score = max(max_score, 0.7)
                    elif intent_type == "availability":
                        max_score = max(max_score, 0.8)

        return max_score
    
    def _count_time_patterns(self, message: str) -> int:
        """Count time-related patterns in the message."""
        time_patterns = [
            r'\d{1,2}:\d{2}',  # Time format (e.g., 14:30, 2:30)
            r'\d{1,2}(am|pm)',  # Time with am/pm
            r'\d{1,2}/\d{1,2}(/\d{2,4})?',  # Date format (e.g., 12/25, 12/25/2023)
            r'\d{1,2}-\d{1,2}(-\d{2,4})?',  # Date format (e.g., 12-25, 12-25-2023)
            r'(next|last|this)\s+(week|month|year|monday|tuesday|wednesday|thursday|friday|saturday|sunday)',
            r'(tomorrow|today|yesterday)',
            r'in\s+\d+\s+(minutes?|hours?|days?|weeks?|months?)',
            r'\d+\s+(minutes?|hours?|days?|weeks?|months?)\s+(ago|from now)'
        ]
        
        count = 0
        for pattern in time_patterns:
            count += len(re.findall(pattern, message, re.IGNORECASE))
        
        return count
    
    def _get_irrelevant_response(self) -> str:
        """Get response for irrelevant messages."""
        return """I'm Zeitwahl, your AI scheduling and time management assistant. 

I specialize in helping with:
• Calendar management and scheduling
• Time zone conversions  
• Meeting planning and coordination
• Productivity and time management advice
• Date and time calculations

Your message doesn't seem to be related to scheduling or time management. How can I help you with your calendar or time-related needs today?"""
    
    async def validate_user_registration_input(self, message_text: str) -> Dict[str, Any]:
        """Validate user input during registration process."""
        # Check for timezone patterns
        timezone_patterns = [
            r'UTC[+-]?\d{0,2}',
            r'GMT[+-]?\d{0,2}',
            r'[A-Z]{3,4}',  # Common timezone abbreviations
            r'[A-Za-z_]+/[A-Za-z_]+',  # IANA timezone format
        ]

        for pattern in timezone_patterns:
            match = re.search(pattern, message_text, re.IGNORECASE)
            if match:
                return {
                    "is_valid_timezone": True,
                    "extracted_timezone": match.group(),
                    "needs_confirmation": True
                }

        return {
            "is_valid_timezone": False,
            "extracted_timezone": None,
            "needs_clarification": True
        }

    def get_validation_stats(self) -> Dict[str, Any]:
        """Get validation statistics for monitoring."""
        return {
            "high_priority_keywords": len(self.high_priority_keywords),
            "medium_priority_keywords": len(self.medium_priority_keywords),
            "temporal_keywords": len(self.temporal_keywords),
            "irrelevant_keywords": len(self.irrelevant_keywords),
            "contextual_keywords": len(self.contextual_keywords),
            "intent_patterns": {intent: len(patterns) for intent, patterns in self.intent_patterns.items()}
        }

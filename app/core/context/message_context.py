"""
Message Context Tracking System for maintaining state consistency across actors.

This module provides a solution for tracking message processing context
across different actors while maintaining asynchronous operations and
ensuring state consistency through the event bus.
"""

import asyncio
import logging
import uuid
from dataclasses import dataclass, field
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional, List
from enum import Enum

logger = logging.getLogger(__name__)


class MessageState(Enum):
    """States of message processing."""
    RECEIVED = "received"
    USER_IDENTIFYING = "user_identifying"
    USER_IDENTIFIED = "user_identified"
    VALIDATING = "validating"
    VALIDATED = "validated"
    CONTEXT_BUILDING = "context_building"
    CONTEXT_BUILT = "context_built"
    LLM_PROCESSING = "llm_processing"
    LLM_PROCESSED = "llm_processed"
    RESPONDING = "responding"
    COMPLETED = "completed"
    ERROR = "error"


@dataclass
class MessageContext:
    """
    Context object that tracks a message through its entire processing lifecycle.
    
    This object maintains all the state and data associated with a message
    as it flows through different actors in the system.
    """
    
    # Message identification
    context_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    telegram_message_id: int = 0
    chat_id: int = 0
    telegram_user_id: Optional[int] = None
    
    # Message content
    message_text: str = ""
    message_type: str = "text"
    
    # User information
    username: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    
    # Telegram metadata
    telegram_timestamp: Optional[datetime] = None
    chat_type: str = "private"
    chat_title: Optional[str] = None
    is_reply: bool = False
    reply_to_message_id: Optional[int] = None
    
    # Processing state
    state: MessageState = MessageState.RECEIVED
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    
    # Processing data
    user_data: Optional[Dict[str, Any]] = None
    validation_result: Optional[Dict[str, Any]] = None
    context_data: Optional[Dict[str, Any]] = None
    conversation_history: List[Dict[str, Any]] = field(default_factory=list)
    prompt: Optional[str] = None
    llm_response: Optional[str] = None
    
    # Processing metadata
    processing_steps: List[Dict[str, Any]] = field(default_factory=list)
    error_info: Optional[Dict[str, Any]] = None
    
    def update_state(self, new_state: MessageState, actor_name: str, data: Optional[Dict[str, Any]] = None):
        """Update the message state and add processing step."""
        self.state = new_state
        self.updated_at = datetime.now(timezone.utc)
        
        step = {
            "state": new_state.value,
            "actor": actor_name,
            "timestamp": self.updated_at,
            "data": data or {}
        }
        self.processing_steps.append(step)
        
        logger.debug(f"Message {self.context_id} state updated to {new_state.value} by {actor_name}")
    
    def add_error(self, error_message: str, actor_name: str, error_details: Optional[Dict[str, Any]] = None):
        """Add error information to the context."""
        self.state = MessageState.ERROR
        self.updated_at = datetime.now(timezone.utc)
        
        self.error_info = {
            "message": error_message,
            "actor": actor_name,
            "timestamp": self.updated_at,
            "details": error_details or {}
        }
        
        logger.error(f"Message {self.context_id} error in {actor_name}: {error_message}")
    
    def get_processing_duration(self) -> float:
        """Get total processing duration in seconds."""
        return (self.updated_at - self.created_at).total_seconds()
    
    def is_completed(self) -> bool:
        """Check if message processing is completed."""
        return self.state in [MessageState.COMPLETED, MessageState.ERROR]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert context to dictionary for serialization."""
        return {
            "context_id": self.context_id,
            "telegram_message_id": self.telegram_message_id,
            "chat_id": self.chat_id,
            "telegram_user_id": self.telegram_user_id,
            "message_text": self.message_text,
            "state": self.state.value,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "processing_duration": self.get_processing_duration(),
            "processing_steps": len(self.processing_steps),
            "has_error": self.error_info is not None
        }


class MessageContextManager:
    """
    Manager for tracking message contexts across the system.
    
    This class maintains a registry of active message contexts and provides
    methods for actors to access and update context information while
    maintaining consistency.
    """
    
    def __init__(self, cleanup_interval: int = 300):  # 5 minutes
        self.contexts: Dict[str, MessageContext] = {}
        self.telegram_id_to_context: Dict[tuple, str] = {}  # (telegram_message_id, chat_id) -> context_id
        self.cleanup_interval = cleanup_interval
        self._cleanup_task: Optional[asyncio.Task] = None
        
        logger.info("MessageContextManager initialized")
    
    def create_context(
        self,
        telegram_message_id: int,
        chat_id: int,
        message_text: str,
        telegram_user_id: Optional[int] = None,
        **kwargs
    ) -> MessageContext:
        """Create a new message context."""
        context = MessageContext(
            telegram_message_id=telegram_message_id,
            chat_id=chat_id,
            message_text=message_text,
            telegram_user_id=telegram_user_id,
            **kwargs
        )
        
        # Store in registries
        self.contexts[context.context_id] = context
        self.telegram_id_to_context[(telegram_message_id, chat_id)] = context.context_id
        
        # Start cleanup task if not running
        self._start_cleanup_task()
        
        logger.debug(f"Created message context {context.context_id} for message {telegram_message_id}")
        return context
    
    def get_context(self, context_id: str) -> Optional[MessageContext]:
        """Get context by context ID."""
        return self.contexts.get(context_id)
    
    def get_context_by_telegram_id(self, telegram_message_id: int, chat_id: int) -> Optional[MessageContext]:
        """Get context by Telegram message ID and chat ID."""
        context_id = self.telegram_id_to_context.get((telegram_message_id, chat_id))
        if context_id:
            return self.contexts.get(context_id)
        return None
    
    def update_context(self, context_id: str, **updates) -> bool:
        """Update context with new data."""
        context = self.contexts.get(context_id)
        if not context:
            logger.warning(f"Context {context_id} not found for update")
            return False
        
        for key, value in updates.items():
            if hasattr(context, key):
                setattr(context, key, value)
        
        context.updated_at = datetime.now(timezone.utc)
        return True
    
    def remove_context(self, context_id: str) -> bool:
        """Remove a context from tracking."""
        context = self.contexts.get(context_id)
        if not context:
            return False
        
        # Remove from both registries
        del self.contexts[context_id]
        telegram_key = (context.telegram_message_id, context.chat_id)
        if telegram_key in self.telegram_id_to_context:
            del self.telegram_id_to_context[telegram_key]
        
        logger.debug(f"Removed context {context_id}")
        return True
    
    def get_active_contexts(self) -> List[MessageContext]:
        """Get all active (non-completed) contexts."""
        return [ctx for ctx in self.contexts.values() if not ctx.is_completed()]
    
    def get_context_stats(self) -> Dict[str, Any]:
        """Get statistics about contexts."""
        total = len(self.contexts)
        active = len(self.get_active_contexts())
        completed = sum(1 for ctx in self.contexts.values() if ctx.state == MessageState.COMPLETED)
        errors = sum(1 for ctx in self.contexts.values() if ctx.state == MessageState.ERROR)
        
        return {
            "total_contexts": total,
            "active_contexts": active,
            "completed_contexts": completed,
            "error_contexts": errors,
            "states": {state.value: sum(1 for ctx in self.contexts.values() if ctx.state == state) 
                      for state in MessageState}
        }
    
    def _start_cleanup_task(self):
        """Start the cleanup task if not already running."""
        if self._cleanup_task is None or self._cleanup_task.done():
            self._cleanup_task = asyncio.create_task(self._cleanup_old_contexts())
    
    async def _cleanup_old_contexts(self):
        """Clean up old completed contexts."""
        while True:
            try:
                await asyncio.sleep(self.cleanup_interval)
                
                cutoff_time = datetime.now(timezone.utc) - timedelta(hours=1)  # Keep for 1 hour
                contexts_to_remove = []
                
                for context_id, context in self.contexts.items():
                    if context.is_completed() and context.updated_at < cutoff_time:
                        contexts_to_remove.append(context_id)
                
                for context_id in contexts_to_remove:
                    self.remove_context(context_id)
                
                if contexts_to_remove:
                    logger.info(f"Cleaned up {len(contexts_to_remove)} old contexts")
                
            except Exception as e:
                logger.error(f"Error in context cleanup: {e}")
    
    async def shutdown(self):
        """Shutdown the context manager."""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        logger.info("MessageContextManager shutdown")


# Global context manager instance
message_context_manager = MessageContextManager()

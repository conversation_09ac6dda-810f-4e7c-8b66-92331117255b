from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, List, Optional
from app.core.events.event_bus import BaseEvent

# Import database events
from app.utils.database_events import *


@dataclass
class MessageReceived(BaseEvent):
    """Event published when a message is received from Telegram."""
    user_id: int
    chat_id: int
    message_id: int
    message_text: str
    username: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    timestamp: Optional[datetime] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class MessagePreprocessed(BaseEvent):
    """Event published when message preprocessing is complete."""
    user_id: int
    chat_id: int
    message_id: int
    original_message: str
    processed_prompt: str
    user_context: Dict[str, Any]
    conversation_history: List[Dict[str, Any]]
    available_tools: List[str]
    timestamp: Optional[datetime] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class LLMResponseReceived(BaseEvent):
    """Event published when LLM response is received."""
    user_id: int
    chat_id: int
    message_id: int
    response_text: str
    tool_calls: List[Dict[str, Any]]
    provider_used: str
    tokens_used: int
    processing_time: float
    timestamp: Optional[datetime] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class ToolExecutionStarted(BaseEvent):
    """Event published when tool execution begins."""
    user_id: int
    chat_id: int
    message_id: int
    tool_name: str
    tool_parameters: Dict[str, Any]
    execution_id: str
    timestamp: Optional[datetime] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class ToolExecutionCompleted(BaseEvent):
    """Event published when tool execution completes."""
    user_id: int
    chat_id: int
    message_id: int
    tool_name: str
    execution_id: str
    result: Any
    success: bool
    error_message: Optional[str] = None
    execution_time: float = 0.0
    timestamp: Optional[datetime] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class ResponseReady(BaseEvent):
    """Event published when final response is ready to send."""
    user_id: int
    chat_id: int
    message_id: int
    response_text: str
    response_type: str = "text"  # text, markdown, html
    timestamp: Optional[datetime] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class ErrorOccurred(BaseEvent):
    """Event published when an error occurs in the system."""
    user_id: Optional[int]
    chat_id: Optional[int]
    message_id: Optional[int]
    error_type: str
    error_message: str
    component: str
    stack_trace: Optional[str] = None
    timestamp: Optional[datetime] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class UserSessionStarted(BaseEvent):
    """Event published when a user session starts."""
    user_id: int
    chat_id: int
    username: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    timestamp: Optional[datetime] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class UserSessionEnded(BaseEvent):
    """Event published when a user session ends."""
    user_id: int
    chat_id: int
    session_duration: float
    messages_count: int
    timestamp: Optional[datetime] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class SystemHealthCheck(BaseEvent):
    """Event published for system health monitoring."""
    component: str
    status: str  # healthy, warning, error
    metrics: Dict[str, Any]
    message: Optional[str] = None
    timestamp: Optional[datetime] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


# Actor-specific events for the Actor-based model

@dataclass
class UserMessageReceived(BaseEvent):
    """Event published by BotActor when a user message is received."""
    context_id: str
    telegram_user_id: int
    chat_id: int
    telegram_message_id: int
    message_text: str
    message_type: str = "text"
    username: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    telegram_timestamp: Optional[datetime] = None
    chat_type: str = "private"
    chat_title: Optional[str] = None
    is_reply: bool = False
    reply_to_message_id: Optional[int] = None
    timestamp: Optional[datetime] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class UserIdentified(BaseEvent):
    """Event published by PreprocessingActor when user is identified."""
    context_id: str
    telegram_user_id: int
    chat_id: int
    telegram_message_id: int
    user_exists: bool
    user_data: Optional[Dict[str, Any]] = None
    needs_registration: bool = False
    timestamp: Optional[datetime] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class ContextBuilt(BaseEvent):
    """Event published by PreprocessingActor when context is built."""
    telegram_user_id: int
    chat_id: int
    telegram_message_id: int
    original_message: str
    context: Dict[str, Any]
    conversation_history: List[Dict[str, Any]]
    prompt: str
    is_relevant: bool
    timestamp: Optional[datetime] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class LLMRequestReady(BaseEvent):
    """Event published when LLM request is ready for processing."""
    context_id: str
    telegram_user_id: int
    chat_id: int
    telegram_message_id: int
    prompt: str
    context: Dict[str, Any]
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None
    timestamp: Optional[datetime] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class LLMResponseReady(BaseEvent):
    """Event published by LLMCallsActor when LLM response is ready."""
    context_id: str
    telegram_user_id: int
    chat_id: int
    telegram_message_id: int
    response_text: str
    provider_used: str
    tokens_used: int
    processing_time: float
    success: bool
    error_message: Optional[str] = None
    timestamp: Optional[datetime] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class SendMessageRequest(BaseEvent):
    """Event published to request BotActor to send a message."""
    chat_id: int
    message_text: str
    context_id: Optional[str] = None
    reply_to_message_id: Optional[int] = None
    parse_mode: Optional[str] = None
    timestamp: Optional[datetime] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class MessageSent(BaseEvent):
    """Event published by BotActor when a message is sent."""
    chat_id: int
    telegram_message_id: int
    message_text: str
    success: bool
    context_id: Optional[str] = None
    error_message: Optional[str] = None
    timestamp: Optional[datetime] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class GenericResponse(BaseEvent):
    """Event published when a generic response should be sent."""
    chat_id: int
    message_type: str  # "new_user", "irrelevant_message", "error"
    reply_to_message_id: Optional[int] = None
    timestamp: Optional[datetime] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

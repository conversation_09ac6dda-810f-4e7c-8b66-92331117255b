"""
Actor base class for the Actor-based model architecture.

This module provides the base Actor class that all actors in the system inherit from.
Actors are asynchronous components similar to Vert.x Verticles that can be started,
stopped, and communicate through the event bus.
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any
from enum import Enum

from app.core.events.event_bus import event_bus

logger = logging.getLogger(__name__)


class ActorState(Enum):
    """Actor lifecycle states."""
    CREATED = "created"
    INITIALIZING = "initializing"
    INITIALIZED = "initialized"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    STOPPED = "stopped"
    ERROR = "error"


class Actor(ABC):
    """
    Base class for all actors in the system.
    
    Actors are asynchronous components that:
    - Have their own lifecycle (start/stop)
    - Have an async initialization method
    - Communicate through the event bus
    - Have their tagged methods automatically subscribed to events
    """
    
    def __init__(self, name: Optional[str] = None):
        """
        Initialize the actor.
        
        Args:
            name: Optional name for the actor. If not provided, uses class name.
        """
        self.name = name or self.__class__.__name__
        self.state = ActorState.CREATED
        self.event_bus = event_bus
        self._start_time: Optional[float] = None
        self._stop_time: Optional[float] = None
        self._initialization_error: Optional[Exception] = None
        
        logger.debug(f"Actor {self.name} created")
    
    @abstractmethod
    async def initialize(self) -> None:
        """
        Initialize the actor.
        
        This method is called once before the actor is started.
        Override this method to perform any initialization logic.
        """
        pass
    
    @abstractmethod
    async def start_actor(self) -> None:
        """
        Start the actor's main functionality.
        
        This method is called after initialization to start the actor's
        main processing loop or set up listeners.
        """
        pass
    
    @abstractmethod
    async def stop_actor(self) -> None:
        """
        Stop the actor and cleanup resources.
        
        This method is called to gracefully stop the actor and
        clean up any resources it's using.
        """
        pass
    
    async def start(self) -> None:
        """
        Start the actor with full lifecycle management.
        
        This method handles the complete startup process:
        1. Initialize the actor
        2. Subscribe to event bus
        3. Start the actor's functionality
        """
        try:
            if self.state != ActorState.CREATED:
                raise RuntimeError(f"Actor {self.name} cannot be started from state {self.state}")
            
            logger.info(f"Starting actor {self.name}")
            self.state = ActorState.INITIALIZING
            
            # Initialize the actor
            await self.initialize()
            self.state = ActorState.INITIALIZED
            
            # Subscribe to event bus
            await self.event_bus.subscribe_tagged_methods(self)
            logger.debug(f"Actor {self.name} subscribed to event bus")
            
            # Start the actor
            self.state = ActorState.STARTING
            await self.start_actor()
            
            self.state = ActorState.RUNNING
            self._start_time = asyncio.get_event_loop().time()
            
            logger.info(f"Actor {self.name} started successfully")
            
        except Exception as e:
            self.state = ActorState.ERROR
            self._initialization_error = e
            logger.error(f"Failed to start actor {self.name}: {e}")
            raise
    
    async def stop(self) -> None:
        """
        Stop the actor with full lifecycle management.
        
        This method handles the complete shutdown process:
        1. Stop the actor's functionality
        2. Unsubscribe from event bus
        3. Clean up resources
        """
        try:
            if self.state not in [ActorState.RUNNING, ActorState.ERROR]:
                logger.warning(f"Actor {self.name} stop called from state {self.state}")
                return
            
            logger.info(f"Stopping actor {self.name}")
            self.state = ActorState.STOPPING
            
            # Stop the actor's functionality
            await self.stop_actor()
            
            # Unsubscribe from event bus
            await self.event_bus.unsubscribe_tagged_methods(self)
            logger.debug(f"Actor {self.name} unsubscribed from event bus")
            
            self.state = ActorState.STOPPED
            self._stop_time = asyncio.get_event_loop().time()
            
            logger.info(f"Actor {self.name} stopped successfully")
            
        except Exception as e:
            self.state = ActorState.ERROR
            logger.error(f"Failed to stop actor {self.name}: {e}")
            raise
    
    def is_running(self) -> bool:
        """Check if the actor is currently running."""
        return self.state == ActorState.RUNNING
    
    def is_stopped(self) -> bool:
        """Check if the actor is stopped."""
        return self.state == ActorState.STOPPED
    
    def has_error(self) -> bool:
        """Check if the actor is in an error state."""
        return self.state == ActorState.ERROR
    
    def get_uptime(self) -> Optional[float]:
        """Get the actor's uptime in seconds."""
        if self._start_time is None:
            return None
        
        end_time = self._stop_time or asyncio.get_event_loop().time()
        return end_time - self._start_time
    
    def get_status(self) -> Dict[str, Any]:
        """Get the actor's current status information."""
        return {
            "name": self.name,
            "state": self.state.value,
            "uptime": self.get_uptime(),
            "start_time": self._start_time,
            "stop_time": self._stop_time,
            "has_error": self.has_error(),
            "initialization_error": str(self._initialization_error) if self._initialization_error else None
        }
    
    def __repr__(self) -> str:
        """String representation of the actor."""
        return f"<{self.__class__.__name__}(name='{self.name}', state='{self.state.value}')>"

"""
Actor System Manager for coordinating all actors in the system.

This module provides the ActorSystem class that manages the lifecycle
of all actors, handles their initialization, starting, stopping, and
provides system-wide coordination.
"""

import asyncio
import logging
import signal
from typing import Dict, List, Optional, Any
from enum import Enum

from app.core.actors.actor import Actor
from app.core.events.event_bus import event_bus

logger = logging.getLogger(__name__)


class ActorSystemState(Enum):
    """Actor system states."""
    CREATED = "created"
    INITIALIZING = "initializing"
    RUNNING = "running"
    STOPPING = "stopping"
    STOPPED = "stopped"
    ERROR = "error"


class ActorSystem:
    """
    Central manager for all actors in the system.
    
    The ActorSystem handles:
    - Actor lifecycle management (initialization, starting, stopping)
    - System-wide coordination and monitoring
    - Graceful shutdown handling
    - Error recovery and system health monitoring
    """
    
    def __init__(self):
        self.state = ActorSystemState.CREATED
        self.actors: Dict[str, Actor] = {}
        self.startup_order: List[str] = []
        self.shutdown_order: List[str] = []
        self._shutdown_event = asyncio.Event()
        self._startup_timeout = 30.0  # seconds
        self._shutdown_timeout = 15.0  # seconds
        
        # Initialize actors
        self._initialize_actors()
        
        # Set up signal handlers for graceful shutdown
        self._setup_signal_handlers()
    
    def _initialize_actors(self):
        """Initialize all actors and define startup/shutdown order."""
        # Import actors lazily to avoid circular imports
        from app.infrastructure.database.db_actor import DBActor
        from app.domain.preprocessing.preprocessing_actor import PreprocessingActor
        from app.infrastructure.llm.llm_calls_actor import LLMCallsActor
        from app.infrastructure.telegram.bot_actor import BotActor

        # Create actor instances
        self.actors = {
            "db_actor": DBActor(),
            "preprocessing_actor": PreprocessingActor(),
            "llm_calls_actor": LLMCallsActor(),
            "bot_actor": BotActor()
        }
        
        # Define startup order (dependencies first)
        self.startup_order = [
            "db_actor",           # Database must be ready first
            "preprocessing_actor", # Preprocessing needs database
            "llm_calls_actor",    # LLM calls can start independently
            "bot_actor"           # Bot starts last (triggers message flow)
        ]
        
        # Define shutdown order (reverse of startup)
        self.shutdown_order = list(reversed(self.startup_order))
        
        logger.info(f"Initialized {len(self.actors)} actors")
    
    def _setup_signal_handlers(self):
        """Set up signal handlers for graceful shutdown."""
        try:
            # Handle SIGINT (Ctrl+C) and SIGTERM
            for sig in [signal.SIGINT, signal.SIGTERM]:
                signal.signal(sig, self._signal_handler)
            
            logger.debug("Signal handlers set up for graceful shutdown")
            
        except Exception as e:
            logger.warning(f"Could not set up signal handlers: {e}")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        _ = frame  # Unused parameter required by signal handler interface
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self._shutdown_event.set()
    
    async def start(self) -> None:
        """Start the actor system."""
        try:
            if self.state != ActorSystemState.CREATED:
                raise RuntimeError(f"ActorSystem cannot be started from state {self.state}")
            
            logger.info("Starting Actor System...")
            self.state = ActorSystemState.INITIALIZING
            
            # Start actors in order
            for actor_name in self.startup_order:
                actor = self.actors[actor_name]
                
                logger.info(f"Starting actor: {actor_name}")
                
                try:
                    # Start actor with timeout
                    await asyncio.wait_for(
                        actor.start(),
                        timeout=self._startup_timeout
                    )
                    
                    logger.info(f"Actor {actor_name} started successfully")
                    
                except asyncio.TimeoutError:
                    raise RuntimeError(f"Actor {actor_name} startup timed out after {self._startup_timeout}s")
                except Exception as e:
                    raise RuntimeError(f"Failed to start actor {actor_name}: {e}")
            
            self.state = ActorSystemState.RUNNING
            logger.info("Actor System started successfully")
            
            # Wait for shutdown signal or error
            await self._wait_for_shutdown()
            
        except Exception as e:
            self.state = ActorSystemState.ERROR
            logger.error(f"Failed to start Actor System: {e}")
            raise
        finally:
            # Always attempt cleanup
            await self.stop()
    
    async def stop(self) -> None:
        """Stop the actor system."""
        if self.state in [ActorSystemState.STOPPED, ActorSystemState.STOPPING]:
            logger.warning("ActorSystem stop called but already stopping/stopped")
            return
        
        try:
            logger.info("Stopping Actor System...")
            self.state = ActorSystemState.STOPPING
            
            # Stop actors in reverse order
            for actor_name in self.shutdown_order:
                actor = self.actors[actor_name]
                
                if not actor.is_running():
                    logger.debug(f"Actor {actor_name} already stopped")
                    continue
                
                logger.info(f"Stopping actor: {actor_name}")
                
                try:
                    # Stop actor with timeout
                    await asyncio.wait_for(
                        actor.stop(),
                        timeout=self._shutdown_timeout
                    )
                    
                    logger.info(f"Actor {actor_name} stopped successfully")
                    
                except asyncio.TimeoutError:
                    logger.error(f"Actor {actor_name} shutdown timed out after {self._shutdown_timeout}s")
                except Exception as e:
                    logger.error(f"Error stopping actor {actor_name}: {e}")
            
            self.state = ActorSystemState.STOPPED
            logger.info("Actor System stopped")
            
        except Exception as e:
            self.state = ActorSystemState.ERROR
            logger.error(f"Error stopping Actor System: {e}")
    
    async def _wait_for_shutdown(self):
        """Wait for shutdown signal or system error."""
        try:
            # Create tasks for monitoring
            shutdown_task = asyncio.create_task(self._shutdown_event.wait())
            health_task = asyncio.create_task(self._monitor_system_health())
            
            # Wait for either shutdown signal or health check failure
            done, pending = await asyncio.wait(
                [shutdown_task, health_task],
                return_when=asyncio.FIRST_COMPLETED
            )
            
            # Cancel pending tasks
            for task in pending:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
            
            # Check if health monitoring detected an issue
            for task in done:
                if task == health_task:
                    try:
                        await task  # This will raise if health check failed
                    except Exception as e:
                        logger.error(f"System health check failed: {e}")
                        raise
            
        except Exception as e:
            logger.error(f"Error in shutdown wait: {e}")
            raise
    
    async def _monitor_system_health(self):
        """Monitor system health and detect failures."""
        while self.state == ActorSystemState.RUNNING:
            try:
                # Check actor health
                failed_actors = []
                for name, actor in self.actors.items():
                    if actor.has_error():
                        failed_actors.append(name)
                
                if failed_actors:
                    raise RuntimeError(f"Actors in error state: {failed_actors}")
                
                # Publish system health check
                await event_bus.publish("system_health_check", {
                    "component": "ActorSystem",
                    "status": "healthy",
                    "metrics": self.get_system_metrics(),
                    "timestamp": asyncio.get_event_loop().time()
                })
                
                # Wait before next health check
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"System health monitoring failed: {e}")
                raise
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status."""
        actor_statuses = {}
        for name, actor in self.actors.items():
            actor_statuses[name] = actor.get_status()
        
        return {
            "system_state": self.state.value,
            "actors": actor_statuses,
            "total_actors": len(self.actors),
            "running_actors": sum(1 for actor in self.actors.values() if actor.is_running()),
            "failed_actors": sum(1 for actor in self.actors.values() if actor.has_error()),
            "startup_order": self.startup_order,
            "shutdown_order": self.shutdown_order
        }
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """Get system metrics for monitoring."""
        metrics = {
            "total_actors": len(self.actors),
            "running_actors": 0,
            "stopped_actors": 0,
            "error_actors": 0,
            "total_uptime": 0.0
        }
        
        for actor in self.actors.values():
            if actor.is_running():
                metrics["running_actors"] += 1
            elif actor.is_stopped():
                metrics["stopped_actors"] += 1
            elif actor.has_error():
                metrics["error_actors"] += 1
            
            uptime = actor.get_uptime()
            if uptime:
                metrics["total_uptime"] += uptime
        
        return metrics
    
    def get_actor(self, name: str) -> Optional[Actor]:
        """Get an actor by name."""
        return self.actors.get(name)
    
    def is_running(self) -> bool:
        """Check if the actor system is running."""
        return self.state == ActorSystemState.RUNNING
    
    def is_healthy(self) -> bool:
        """Check if the system is healthy."""
        if self.state != ActorSystemState.RUNNING:
            return False
        
        # Check if all actors are running without errors
        for actor in self.actors.values():
            if not actor.is_running() or actor.has_error():
                return False
        
        return True

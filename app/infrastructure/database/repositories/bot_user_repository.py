"""
Bot User Repository for managing user data in MongoDB.

This repository handles all database operations related to bot users,
including creation, retrieval, updates, and user management.
"""

import logging
from typing import Optional, List
from datetime import datetime, timezone, timedelta

from ..models.bot_user import Bot<PERSON>ser
from ..connection_manager import get_connection_manager

logger = logging.getLogger(__name__)


class BotUserRepository:
    """Repository for bot user operations."""
    
    def __init__(self):
        self.collection_name = "bot_users"
        self.connection_manager = get_connection_manager()
    
    async def create_user(self, user: BotUser) -> BotUser:
        """Create a new user in the database."""
        try:
            collection = self.connection_manager.get_collection(self.collection_name)
            
            # Convert user to dict for MongoDB
            user_dict = user.to_dict()
            
            # Insert the user
            result = collection.insert_one(user_dict)
            
            # Update the user with the generated ID
            user.id = result.inserted_id
            
            logger.debug(f"Created user {user.telegram_user_id}")
            return user
            
        except Exception as e:
            logger.error(f"Error creating user: {e}")
            raise
    
    async def get_user_by_telegram_id(self, telegram_user_id: int) -> Optional[BotUser]:
        """Get a user by their Telegram user ID."""
        try:
            collection = self.connection_manager.get_collection(self.collection_name)
            
            # Find the user
            user_doc = collection.find_one({"telegram_user_id": telegram_user_id})
            
            if user_doc:
                return BotUser.from_dict(user_doc)
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting user by telegram ID {telegram_user_id}: {e}")
            raise
    
    async def update_user(self, user: BotUser) -> BotUser:
        """Update an existing user."""
        try:
            collection = self.connection_manager.get_collection(self.collection_name)
            
            # Update the user
            user_dict = user.to_dict()
            user_dict["updated_at"] = datetime.now(timezone.utc)
            
            result = collection.update_one(
                {"telegram_user_id": user.telegram_user_id},
                {"$set": user_dict}
            )
            
            if result.matched_count == 0:
                raise ValueError(f"User {user.telegram_user_id} not found")
            
            logger.debug(f"Updated user {user.telegram_user_id}")
            return user
            
        except Exception as e:
            logger.error(f"Error updating user {user.telegram_user_id}: {e}")
            raise
    
    async def delete_user(self, telegram_user_id: int) -> bool:
        """Delete a user by their Telegram user ID."""
        try:
            collection = self.connection_manager.get_collection(self.collection_name)
            
            result = collection.delete_one({"telegram_user_id": telegram_user_id})
            
            if result.deleted_count > 0:
                logger.debug(f"Deleted user {telegram_user_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error deleting user {telegram_user_id}: {e}")
            raise
    
    async def update_last_seen(self, telegram_user_id: int) -> bool:
        """Update a user's last seen timestamp."""
        try:
            collection = self.connection_manager.get_collection(self.collection_name)
            
            result = collection.update_one(
                {"telegram_user_id": telegram_user_id},
                {"$set": {"last_seen_at": datetime.now(timezone.utc)}}
            )
            
            return result.matched_count > 0
            
        except Exception as e:
            logger.error(f"Error updating last seen for user {telegram_user_id}: {e}")
            raise
    
    async def increment_message_count(self, telegram_user_id: int) -> bool:
        """Increment a user's message count."""
        try:
            collection = self.connection_manager.get_collection(self.collection_name)
            
            result = collection.update_one(
                {"telegram_user_id": telegram_user_id},
                {"$inc": {"total_messages": 1}}
            )
            
            return result.matched_count > 0
            
        except Exception as e:
            logger.error(f"Error incrementing message count for user {telegram_user_id}: {e}")
            raise
    
    async def get_all_users(self, limit: int = 100, offset: int = 0) -> List[BotUser]:
        """Get all users with pagination."""
        try:
            collection = self.connection_manager.get_collection(self.collection_name)
            
            cursor = collection.find().skip(offset).limit(limit)
            users = []
            
            for user_doc in cursor:
                users.append(BotUser.from_dict(user_doc))
            
            return users
            
        except Exception as e:
            logger.error(f"Error getting all users: {e}")
            raise
    
    async def get_active_users(self, days: int = 7) -> List[BotUser]:
        """Get users who have been active in the last N days."""
        try:
            collection = self.connection_manager.get_collection(self.collection_name)
            
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)
            
            cursor = collection.find({
                "last_seen_at": {"$gte": cutoff_date}
            })
            
            users = []
            for user_doc in cursor:
                users.append(BotUser.from_dict(user_doc))
            
            return users
            
        except Exception as e:
            logger.error(f"Error getting active users: {e}")
            raise
    
    async def get_user_count(self) -> int:
        """Get total number of users."""
        try:
            collection = self.connection_manager.get_collection(self.collection_name)
            return collection.count_documents({})
            
        except Exception as e:
            logger.error(f"Error getting user count: {e}")
            raise

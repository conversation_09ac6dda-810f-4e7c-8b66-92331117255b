"""
MongoDB Connection Manager for database operations.

This module provides connection management for MongoDB Atlas,
handling connection pooling, configuration, and collection access.
"""

import logging
from typing import Optional, Dict, Any
from pymongo import MongoClient
from pymongo.collection import Collection
from pymongo.database import Database
from pymongo.errors import ConnectionFailure, PyMongoError

from app.config import settings
from .interfaces import IDatabaseConnection

logger = logging.getLogger(__name__)


class ConnectionManager(IDatabaseConnection):
    """
    MongoDB connection manager for handling database connections.
    
    Provides connection pooling, configuration management,
    and collection access for the application.
    """
    
    def __init__(self):
        self._client: Optional[MongoClient] = None
        self._database: Optional[Database] = None
        self._connected = False
    
    def connect(self) -> None:
        """Establish connection to MongoDB."""
        if self._connected:
            logger.debug("Already connected to MongoDB")
            return
        
        try:
            logger.info("Connecting to MongoDB...")
            
            # Create MongoDB client
            self._client = MongoClient(
                settings.database.mongodb_url,
                serverSelectionTimeoutMS=5000,  # 5 second timeout
                connectTimeoutMS=10000,         # 10 second connection timeout
                socketTimeoutMS=20000,          # 20 second socket timeout
                maxPoolSize=50,                 # Maximum connections in pool
                minPoolSize=5,                  # Minimum connections in pool
                maxIdleTimeMS=30000,           # 30 second max idle time
            )
            
            # Get database
            self._database = self._client[settings.database.database_name]
            
            # Test connection
            self._client.admin.command('ping')
            
            self._connected = True
            logger.info(f"Connected to MongoDB database: {settings.database.database_name}")
            
        except ConnectionFailure as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error connecting to MongoDB: {e}")
            raise
    
    def disconnect(self) -> None:
        """Close MongoDB connection."""
        if not self._connected:
            return
        
        try:
            if self._client is not None:
                self._client.close()
                logger.info("Disconnected from MongoDB")
            
            self._client = None
            self._database = None
            self._connected = False
            
        except Exception as e:
            logger.error(f"Error disconnecting from MongoDB: {e}")
    
    def get_database(self) -> Database:
        """Get the MongoDB database instance."""
        if not self._connected:
            self.connect()

        if self._database is None:
            raise RuntimeError("Database not available")

        return self._database
    
    def get_collection(self, collection_name: str) -> Collection:
        """Get a MongoDB collection."""
        database = self.get_database()
        return database[collection_name]
    
    def is_connected(self) -> bool:
        """Check if connected to MongoDB."""
        if not self._connected or self._client is None:
            return False
        
        try:
            # Ping the database to check connection
            self._client.admin.command('ping')
            return True
        except Exception:
            self._connected = False
            return False
    
    def get_connection_info(self) -> Dict[str, Any]:
        """Get connection information."""
        return {
            "connected": self._connected,
            "database_name": settings.database.database_name,
            "mongodb_url": settings.database.mongodb_url.split('@')[-1] if '@' in settings.database.mongodb_url else settings.database.mongodb_url,  # Hide credentials
            "client_info": str(self._client) if self._client is not None else None
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on the database connection."""
        health_info = {
            "status": "unknown",
            "connected": False,
            "response_time": None,
            "error": None
        }
        
        try:
            if not self._connected:
                health_info["status"] = "disconnected"
                health_info["error"] = "Not connected to database"
                return health_info
            
            # Measure response time
            import time
            start_time = time.time()
            
            # Ping the database
            result = self._client.admin.command('ping')
            
            response_time = time.time() - start_time
            
            if result.get('ok') == 1:
                health_info["status"] = "healthy"
                health_info["connected"] = True
                health_info["response_time"] = response_time
            else:
                health_info["status"] = "unhealthy"
                health_info["error"] = "Ping command failed"
            
        except Exception as e:
            health_info["status"] = "unhealthy"
            health_info["error"] = str(e)
            self._connected = False
        
        return health_info
    
    def __enter__(self):
        """Context manager entry."""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.disconnect()


# Global connection manager instance
_connection_manager: Optional[ConnectionManager] = None


def get_connection_manager() -> ConnectionManager:
    """Get the global connection manager instance."""
    global _connection_manager
    
    if _connection_manager is None:
        _connection_manager = ConnectionManager()
    
    return _connection_manager


def reset_connection_manager() -> None:
    """Reset the global connection manager (useful for testing)."""
    global _connection_manager
    
    if _connection_manager:
        _connection_manager.disconnect()
    
    _connection_manager = None

"""
Database state manager for maintaining consistency during async operations.

This module provides utilities to maintain state consistency when using
the event bus for database operations, ensuring that the message processing
flow can access required data even with async communication.
"""

import asyncio
import logging
import uuid
from datetime import datetime, timed<PERSON><PERSON>
from typing import Any, Dict, Optional, TypeVar
from dataclasses import dataclass

from app.core.event_bus import event_bus
from app.utils.database_events import DatabaseRequest, DatabaseResponse

logger = logging.getLogger(__name__)

T = TypeVar('T')


@dataclass
class PendingRequest:
    """Represents a pending database request."""
    request_id: str
    future: asyncio.Future
    created_at: datetime
    timeout_seconds: float = 30.0


class DatabaseStateManager:
    """
    Manages state consistency for database operations via event bus.
    
    This class provides a way to make database requests through the event bus
    while maintaining synchronous-like behavior for the calling code, ensuring
    state consistency during message processing.
    """
    
    def __init__(self, default_timeout: float = 30.0):
        self.pending_requests: Dict[str, PendingRequest] = {}
        self.default_timeout = default_timeout
        self._cleanup_task: Optional[asyncio.Task] = None

        # Subscribe to all database response events
        event_bus.subscribe_tagged_methods(self)

        logger.info("DatabaseStateManager initialized")
    
    def _start_cleanup_task(self) -> None:
        """Start the cleanup task for expired requests."""
        if self._cleanup_task is None or self._cleanup_task.done():
            self._cleanup_task = asyncio.create_task(self._cleanup_expired_requests())
    
    async def _cleanup_expired_requests(self) -> None:
        """Periodically clean up expired requests."""
        while True:
            try:
                await asyncio.sleep(10)  # Check every 10 seconds
                
                current_time = datetime.now()
                expired_requests = []
                
                for request_id, pending in self.pending_requests.items():
                    if current_time - pending.created_at > timedelta(seconds=pending.timeout_seconds):
                        expired_requests.append(request_id)
                
                for request_id in expired_requests:
                    pending = self.pending_requests.pop(request_id, None)
                    if pending and not pending.future.done():
                        pending.future.set_exception(
                            TimeoutError(f"Database request {request_id} timed out")
                        )
                        logger.warning(f"Database request {request_id} timed out")
                
            except Exception as e:
                logger.error(f"Error in cleanup task: {e}")
    
    async def request_with_response(
        self,
        request_event: DatabaseRequest,
        response_type: type,
        timeout: Optional[float] = None
    ) -> Any:
        """
        Send a database request and wait for the response.

        Args:
            request_event: The database request event to send
            response_type: The expected response event type
            timeout: Timeout in seconds (uses default if None)

        Returns:
            The response event

        Raises:
            TimeoutError: If the request times out
            Exception: If the database operation fails
        """
        if timeout is None:
            timeout = self.default_timeout

        # Start cleanup task if not already running
        self._start_cleanup_task()

        # Generate unique request ID if not set
        if not hasattr(request_event, 'request_id') or not request_event.request_id:
            request_event.request_id = str(uuid.uuid4())

        # Create future for the response
        future = asyncio.Future()

        # Store pending request
        self.pending_requests[request_event.request_id] = PendingRequest(
            request_id=request_event.request_id,
            future=future,
            created_at=datetime.now(),
            timeout_seconds=timeout
        )
        
        try:
            # Publish the request
            await event_bus.publish(request_event)
            
            # Wait for response with timeout
            response = await asyncio.wait_for(future, timeout=timeout)
            
            # Check if the operation was successful
            if hasattr(response, 'success') and not response.success:
                error_msg = getattr(response, 'error_message', 'Unknown database error')
                raise Exception(f"Database operation failed: {error_msg}")
            
            return response
            
        except asyncio.TimeoutError:
            # Clean up pending request
            self.pending_requests.pop(request_event.request_id, None)
            raise TimeoutError(f"Database request {request_event.request_id} timed out after {timeout} seconds")
        
        except Exception as e:
            # Clean up pending request
            self.pending_requests.pop(request_event.request_id, None)
            raise
    
    def _handle_response(self, response: DatabaseResponse) -> None:
        """Handle a database response event."""
        pending = self.pending_requests.pop(response.request_id, None)
        if pending and not pending.future.done():
            pending.future.set_result(response)
    
    # Event handlers for all database response types
    @event_bus.subscribe("CreateUserResponse")
    async def handle_create_user_response(self, event) -> None:
        self._handle_response(event)
    
    @event_bus.subscribe("GetUserByTelegramIdResponse")
    async def handle_get_user_by_telegram_id_response(self, event) -> None:
        self._handle_response(event)
    
    @event_bus.subscribe("GetUserByIdResponse")
    async def handle_get_user_by_id_response(self, event) -> None:
        self._handle_response(event)
    
    @event_bus.subscribe("UpdateUserResponse")
    async def handle_update_user_response(self, event) -> None:
        self._handle_response(event)
    
    @event_bus.subscribe("UpdateUserLastSeenResponse")
    async def handle_update_user_last_seen_response(self, event) -> None:
        self._handle_response(event)
    
    @event_bus.subscribe("IncrementUserMessageCountResponse")
    async def handle_increment_user_message_count_response(self, event) -> None:
        self._handle_response(event)
    
    @event_bus.subscribe("CreateMessageResponse")
    async def handle_create_message_response(self, event) -> None:
        self._handle_response(event)
    
    @event_bus.subscribe("GetMessageByTelegramIdResponse")
    async def handle_get_message_by_telegram_id_response(self, event) -> None:
        self._handle_response(event)
    
    @event_bus.subscribe("GetMessageByIdResponse")
    async def handle_get_message_by_id_response(self, event) -> None:
        self._handle_response(event)
    
    @event_bus.subscribe("UpdateMessageResponse")
    async def handle_update_message_response(self, event) -> None:
        self._handle_response(event)
    
    @event_bus.subscribe("GetConversationHistoryResponse")
    async def handle_get_conversation_history_response(self, event) -> None:
        self._handle_response(event)
    
    @event_bus.subscribe("GetRecentMessagesResponse")
    async def handle_get_recent_messages_response(self, event) -> None:
        self._handle_response(event)
    
    @event_bus.subscribe("GetUnprocessedMessagesResponse")
    async def handle_get_unprocessed_messages_response(self, event) -> None:
        self._handle_response(event)
    
    @event_bus.subscribe("MarkMessageAsProcessedResponse")
    async def handle_mark_message_as_processed_response(self, event) -> None:
        self._handle_response(event)
    
    @event_bus.subscribe("InitializeDatabaseResponse")
    async def handle_initialize_database_response(self, event) -> None:
        self._handle_response(event)
    
    @event_bus.subscribe("DatabaseHealthCheckResponse")
    async def handle_database_health_check_response(self, event) -> None:
        self._handle_response(event)
    
    async def cleanup(self) -> None:
        """Clean up the state manager."""
        if self._cleanup_task and not self._cleanup_task.done():
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        # Cancel all pending requests
        for pending in self.pending_requests.values():
            if not pending.future.done():
                pending.future.cancel()
        
        self.pending_requests.clear()
        logger.info("DatabaseStateManager cleaned up")


class DatabaseClient:
    """
    High-level client for database operations with state consistency.
    
    This class provides a convenient interface for making database requests
    while maintaining state consistency during the message processing flow.
    """
    
    def __init__(self, state_manager: Optional[DatabaseStateManager] = None):
        self.state_manager = state_manager or DatabaseStateManager()
    
    async def get_user_by_telegram_id(self, telegram_user_id: int, timeout: Optional[float] = None):
        """Get a user by Telegram ID."""
        from app.utils.database_events import GetUserByTelegramIdRequest, GetUserByTelegramIdResponse
        
        request = GetUserByTelegramIdRequest(
            request_id=str(uuid.uuid4()),
            telegram_user_id=telegram_user_id
        )
        
        response = await self.state_manager.request_with_response(
            request, GetUserByTelegramIdResponse, timeout
        )
        
        return response.user
    
    async def create_user(self, user, timeout: Optional[float] = None):
        """Create a new user."""
        from app.utils.database_events import CreateUserRequest, CreateUserResponse
        
        request = CreateUserRequest(
            request_id=str(uuid.uuid4()),
            user=user
        )
        
        response = await self.state_manager.request_with_response(
            request, CreateUserResponse, timeout
        )
        
        return response.user
    
    async def create_message(self, message, timeout: Optional[float] = None):
        """Create a new message."""
        from app.utils.database_events import CreateMessageRequest, CreateMessageResponse
        
        request = CreateMessageRequest(
            request_id=str(uuid.uuid4()),
            message=message
        )
        
        response = await self.state_manager.request_with_response(
            request, CreateMessageResponse, timeout
        )
        
        return response.message
    
    async def get_conversation_history(self, chat_id: int, telegram_user_id: int, 
                                     limit: int = 50, offset: int = 0, 
                                     timeout: Optional[float] = None):
        """Get conversation history."""
        from app.utils.database_events import GetConversationHistoryRequest, GetConversationHistoryResponse
        
        request = GetConversationHistoryRequest(
            request_id=str(uuid.uuid4()),
            chat_id=chat_id,
            telegram_user_id=telegram_user_id,
            limit=limit,
            offset=offset
        )
        
        response = await self.state_manager.request_with_response(
            request, GetConversationHistoryResponse, timeout
        )
        
        return response.messages
    
    async def update_user_last_seen(self, telegram_user_id: int, timeout: Optional[float] = None):
        """Update user's last seen timestamp."""
        from app.utils.database_events import UpdateUserLastSeenRequest, UpdateUserLastSeenResponse
        
        request = UpdateUserLastSeenRequest(
            request_id=str(uuid.uuid4()),
            telegram_user_id=telegram_user_id
        )
        
        response = await self.state_manager.request_with_response(
            request, UpdateUserLastSeenResponse, timeout
        )
        
        return response.updated
    
    async def increment_user_message_count(self, telegram_user_id: int, timeout: Optional[float] = None):
        """Increment user's message count."""
        from app.utils.database_events import IncrementUserMessageCountRequest, IncrementUserMessageCountResponse
        
        request = IncrementUserMessageCountRequest(
            request_id=str(uuid.uuid4()),
            telegram_user_id=telegram_user_id
        )
        
        response = await self.state_manager.request_with_response(
            request, IncrementUserMessageCountResponse, timeout
        )
        
        return response.updated


# Global instances
database_state_manager = DatabaseStateManager()
database_client = DatabaseClient(database_state_manager)

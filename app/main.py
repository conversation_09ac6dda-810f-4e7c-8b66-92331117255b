#!/usr/bin/env python3
"""
Zeitwahl AI Agent - Main Application Entry Point

This module orchestrates the entire Actor-based system, initializing the ActorSystem
and managing the lifecycle of all actors in the application.
"""

import asyncio
import sys

from app.config import settings
from app.utils.logging.logging_config import setup_enhanced_logging, get_logger
from app.core.actors.actor_system import ActorSystem
from app.infrastructure.database.db_actor import DBActor
from app.domain.preprocessing import PreprocessingActor
from app.infrastructure.telegram import BotActor
from app.llmapi import LLMCallsActor


class ZeitwählApp:
    """Main application class that orchestrates the Actor-based system."""

    def __init__(self):
        self.actors = {}
        self.running = False

        # Setup logging
        self._setup_logging()
    
    def _setup_logging(self):
        """Configure enhanced logging for the application."""
        # Set up enhanced logging with colors and emojis
        setup_enhanced_logging(
            log_level=settings.app.log_level,
            use_colors=True,
            show_emojis=True,
            log_to_file=settings.app.environment == "production",
            log_file_path="zeitwahl.log" if settings.app.environment == "production" else None
        )

        logger = get_logger(__name__)
        logger.info(f"🚀 Enhanced logging configured at {settings.app.log_level} level")

    async def initialize(self):
        """Initialize the Actor-based system."""
        logger = get_logger(__name__)
        logger.info("Initializing Zeitwahl AI Agent with Actor-based architecture...")

        try:
            # Validate configuration
            await self._validate_configuration()

            # Initialize actors
            logger.info("Initializing actors...")

            # Option 1: Dependency injection (recommended for testing and explicit dependencies)
            # from app.infrastructure.llm.services.llm_service import LLMService
            # from app.infrastructure.llm.services.token_calculator import TokenCalculator
            # llm_service = LLMService()
            # token_calculator = TokenCalculator()
            # llm_calls_actor = LLMCallsActor(llm_service=llm_service, token_calculator=token_calculator)

            # Option 2: Auto-initialization (simpler, used here)
            self.actors = {
                "db_actor": DBActor(),
                "preprocessing_actor": PreprocessingActor(),
                "llm_calls_actor": LLMCallsActor(),  # Services auto-initialized in constructor
                "bot_actor": BotActor()
            }

            # Initialize each actor
            for name, actor in self.actors.items():
                logger.info(f"Initializing {name}...")
                await actor.initialize()

            logger.info("All actors initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize application: {e}")
            raise
    
    async def _validate_configuration(self):
        """Validate critical configuration settings."""
        logger = get_logger(__name__)

        # Check required settings
        if not settings.telegram.bot_token:
            raise ValueError("TELEGRAM_BOT_TOKEN is required")

        # Warn about missing optional settings
        if not settings.llm.gemini_api_key and not settings.llm.deepseek_api_key:
            logger.warning("No LLM API keys configured, using mock provider only")

        if not settings.calendar.google_client_id and not settings.calendar.outlook_client_id:
            logger.warning("No calendar integrations configured, using mock provider only")

        logger.info("Configuration validation completed")

    async def start(self):
        """Start the Actor-based application."""
        logger = get_logger(__name__)

        if self.running:
            logger.warning("Application is already running")
            return

        try:
            logger.info("Starting Zeitwahl AI Agent with actors...")
            self.running = True

            # Start actors in dependency order
            startup_order = ["db_actor", "preprocessing_actor", "llm_calls_actor", "bot_actor"]

            for actor_name in startup_order:
                actor = self.actors[actor_name]
                logger.info(f"Starting {actor_name}...")
                await actor.start()
                logger.info(f"{actor_name} started successfully")

            logger.info("All actors started successfully")

            # Keep the application running
            import signal
            import asyncio

            # Set up signal handlers for graceful shutdown
            def signal_handler(signum, _frame):
                logger.info(f"Received signal {signum}, shutting down...")
                asyncio.create_task(self.shutdown())

            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)

            # Wait indefinitely
            while self.running:
                await asyncio.sleep(1)

        except Exception as e:
            logger.error(f"Failed to start application: {e}")
            await self.shutdown()
            raise

    async def shutdown(self):
        """Gracefully shutdown the Actor-based application."""
        logger = get_logger(__name__)

        if not self.running:
            return

        logger.info("Shutting down Zeitwahl AI Agent...")
        self.running = False

        try:
            # Stop actors in reverse order
            shutdown_order = ["bot_actor", "llm_calls_actor", "preprocessing_actor", "db_actor"]

            for actor_name in shutdown_order:
                if actor_name in self.actors:
                    actor = self.actors[actor_name]
                    logger.info(f"Stopping {actor_name}...")
                    try:
                        await actor.stop()
                        logger.info(f"{actor_name} stopped successfully")
                    except Exception as e:
                        logger.error(f"Error stopping {actor_name}: {e}")

            logger.info("Shutdown completed successfully")

        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
    
    async def health_check(self) -> dict:
        """Perform application health check."""
        health_status = {
            "status": "healthy",
            "timestamp": asyncio.get_event_loop().time(),
            "components": {},
            "actors": {}
        }

        try:
            # Check individual actors
            for name, actor in self.actors.items():
                if actor.is_running():
                    health_status["actors"][name] = "running"
                elif actor.has_error():
                    health_status["actors"][name] = "error"
                    health_status["status"] = "degraded"
                else:
                    health_status["actors"][name] = "stopped"
                    health_status["status"] = "degraded"

            # Overall system status
            running_actors = sum(1 for actor in self.actors.values() if actor.is_running())
            total_actors = len(self.actors)

            health_status["components"]["total_actors"] = total_actors
            health_status["components"]["running_actors"] = running_actors
            health_status["components"]["application_running"] = self.running

            if running_actors == 0:
                health_status["status"] = "stopped"
            elif running_actors < total_actors:
                health_status["status"] = "degraded"

        except Exception as e:
            health_status["status"] = "unhealthy"
            health_status["error"] = str(e)

        return health_status


async def main():
    """Main entry point for the application."""
    logger = get_logger(__name__)
    
    try:
        # Create and initialize the application
        app = ZeitwählApp()
        await app.initialize()
        
        # Start the application
        logger.info("🚀 Zeitwahl AI Agent is starting...")
        logger.info(f"Environment: {settings.app.environment}")
        logger.info(f"Debug mode: {settings.app.debug}")
        
        await app.start()
        
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Application failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    # Run the application
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nGracefully shutting down...")
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)

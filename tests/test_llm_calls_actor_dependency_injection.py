"""
Test cases demonstrating the dependency injection solution for LLMCallsActor.

These tests show how dependency injection solves the type hint issue and
improves testability.
"""

import pytest
from unittest.mock import Mock, AsyncMock
from typing import Dict, Any

from app.infrastructure.llm.llm_calls_actor import LLMCallsActor
from app.infrastructure.llm.services.llm_service import LLMService
from app.infrastructure.llm.services.token_calculator import TokenCalculator


class MockLLMService:
    """Mock LLM service for testing."""
    
    def __init__(self):
        self.primary_provider = "mock"
        self.fallback_providers = ["backup_mock"]
        self.providers = {"mock": Mock(), "backup_mock": Mock()}
    
    async def generate_response(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Mock generate response."""
        return {
            "content": f"Mock response to: {prompt}",
            "provider": self.primary_provider,
            "tokens_used": 50,
            "tool_calls": [],
            "processing_time": 0.1
        }


class MockTokenCalculator:
    """Mock token calculator for testing."""
    
    def estimate_tokens(self, text: str) -> int:
        """Mock token estimation."""
        return len(text.split())
    
    def optimize_prompt_length(self, prompt: str, provider: str, max_response_tokens: int = 1000):
        """Mock prompt optimization."""
        return prompt, {
            "was_truncated": False,
            "original_tokens": self.estimate_tokens(prompt),
            "optimized_tokens": self.estimate_tokens(prompt)
        }
    
    def get_model_limit(self, model_name: str) -> int:
        """Mock model limit."""
        return 4000
    
    def calculate_remaining_tokens(self, text: str, model_name: str) -> int:
        """Mock remaining tokens calculation."""
        return self.get_model_limit(model_name) - self.estimate_tokens(text)


class TestLLMCallsActorDependencyInjection:
    """Test cases for LLMCallsActor dependency injection."""
    
    def test_dependency_injection_constructor(self):
        """Test that dependency injection works in constructor."""
        # Arrange
        mock_llm_service = MockLLMService()
        mock_token_calculator = MockTokenCalculator()
        
        # Act
        actor = LLMCallsActor(
            name="TestActor",
            llm_service=mock_llm_service,
            token_calculator=mock_token_calculator
        )
        
        # Assert
        assert actor.llm_service is mock_llm_service
        assert actor.token_calculator is mock_token_calculator
        assert actor.name == "TestActor"
        
        # Type hints should be satisfied (no None values)
        assert actor.llm_service is not None
        assert actor.token_calculator is not None
    
    def test_auto_initialization_constructor(self):
        """Test that auto-initialization works when no dependencies provided."""
        # Act
        actor = LLMCallsActor(name="AutoActor")
        
        # Assert
        assert actor.llm_service is not None
        assert actor.token_calculator is not None
        assert isinstance(actor.llm_service, LLMService)
        assert isinstance(actor.token_calculator, TokenCalculator)
        assert actor.name == "AutoActor"
    
    def test_mixed_dependency_injection(self):
        """Test mixed approach - some injected, some auto-created."""
        # Arrange
        mock_llm_service = MockLLMService()
        
        # Act
        actor = LLMCallsActor(
            name="MixedActor",
            llm_service=mock_llm_service
            # token_calculator will be auto-created
        )
        
        # Assert
        assert actor.llm_service is mock_llm_service
        assert actor.token_calculator is not None
        assert isinstance(actor.token_calculator, TokenCalculator)
    
    def test_default_name_when_none_provided(self):
        """Test that default name is used when none provided."""
        # Act
        actor = LLMCallsActor()
        
        # Assert
        assert actor.name == "LLMCallsActor"
        assert actor.llm_service is not None
        assert actor.token_calculator is not None
    
    @pytest.mark.asyncio
    async def test_initialize_with_injected_dependencies(self):
        """Test that initialization works with injected dependencies."""
        # Arrange
        mock_llm_service = MockLLMService()
        mock_token_calculator = MockTokenCalculator()
        
        actor = LLMCallsActor(
            llm_service=mock_llm_service,
            token_calculator=mock_token_calculator
        )
        
        # Act
        await actor.initialize()
        
        # Assert - should not raise any exceptions
        assert actor.llm_service is mock_llm_service
        assert actor.token_calculator is mock_token_calculator
    
    @pytest.mark.asyncio
    async def test_services_are_usable_after_injection(self):
        """Test that injected services are actually usable."""
        # Arrange
        mock_llm_service = MockLLMService()
        mock_token_calculator = MockTokenCalculator()
        
        actor = LLMCallsActor(
            llm_service=mock_llm_service,
            token_calculator=mock_token_calculator
        )
        
        await actor.initialize()
        
        # Act & Assert - Test LLM service
        response = await actor.llm_service.generate_response("test prompt")
        assert response["content"] == "Mock response to: test prompt"
        assert response["provider"] == "mock"
        assert response["tokens_used"] == 50
        
        # Act & Assert - Test token calculator
        tokens = actor.token_calculator.estimate_tokens("hello world")
        assert tokens == 2
        
        optimized, info = actor.token_calculator.optimize_prompt_length("test prompt", "mock")
        assert optimized == "test prompt"
        assert not info["was_truncated"]
    
    def test_type_hints_are_satisfied(self):
        """Test that type hints are properly satisfied (no Optional[Service])."""
        # This test verifies that the type checker will be happy
        
        # Arrange
        mock_llm_service = MockLLMService()
        mock_token_calculator = MockTokenCalculator()
        
        # Act
        actor = LLMCallsActor(
            llm_service=mock_llm_service,
            token_calculator=mock_token_calculator
        )
        
        # Assert - These should not trigger type checker warnings
        # because the attributes are typed as non-Optional
        llm_service: LLMService = actor.llm_service  # No type error
        token_calculator: TokenCalculator = actor.token_calculator  # No type error
        
        # Verify they're actually the injected instances
        assert llm_service is mock_llm_service
        assert token_calculator is mock_token_calculator
    
    def test_backwards_compatibility(self):
        """Test that existing code without dependency injection still works."""
        # This ensures we didn't break existing usage patterns
        
        # Act - Old style instantiation
        actor = LLMCallsActor()
        
        # Assert - Should still work
        assert actor.llm_service is not None
        assert actor.token_calculator is not None
        assert actor.name == "LLMCallsActor"


if __name__ == "__main__":
    # Run a simple test to verify the solution
    import asyncio
    
    async def quick_test():
        print("🧪 Quick test of dependency injection solution...")
        
        # Test dependency injection
        mock_llm = MockLLMService()
        mock_calc = MockTokenCalculator()
        
        actor = LLMCallsActor(
            name="QuickTest",
            llm_service=mock_llm,
            token_calculator=mock_calc
        )
        
        await actor.initialize()
        
        # Verify no None values (type hint issue solved)
        assert actor.llm_service is not None
        assert actor.token_calculator is not None
        
        # Test functionality
        response = await actor.llm_service.generate_response("test")
        tokens = actor.token_calculator.estimate_tokens("hello world")
        
        print(f"✅ Actor created: {actor.name}")
        print(f"✅ LLM Service working: {response['content']}")
        print(f"✅ Token Calculator working: {tokens} tokens")
        print("✅ Type hint issue resolved - no None values!")
    
    asyncio.run(quick_test())
